<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 載入必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
    <script src="../../lib/gpt-3-encoder/browser.js" charset="utf-8" async></script>

    <script>
        document.oncontextmenu = new Function("return false");
        oncontextmenu = "return false;"
    </script>
</head>

<body onload="Chat.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="./chat.js" charset="utf-8"></script>
    <script src="./MessageController.js?v=8.0.10.b1" charset="utf-8"></script>
    <script src="./messageQbi/QbiAnswer.js?v=8.0.10.b2" charset="utf-8"></script>
    <script src="./action/ActionManager.js?v=8.0.10.b1" charset="utf-8"></script>
    <script src="./chatEvent.js?v=8.0.10.b1" charset="utf-8"></script>
    <script src="./chatCatalog.js?v=8.0.12.b2" charset="utf-8"></script>
    <link rel=stylesheet type="text/css" href="./message/css/MessageStyle.css" charset="utf-8">

    <!-- demo -->
    <script src="./demo/demo.js" charset="utf-8"></script>
    <link rel=stylesheet type="text/css" href="./chat.css" charset="utf-8">

    <div id="App" style="display: none;">
        <div id="DirKnowledge" class="DirKnowledge text-center catalog" v-on:click="openKMSelectView">
            <font class="catalogContent" v-bind:title="NowCatalog.hint">{{T['catalogNow']}}：{{NowCatalog.FName |
                truncate}}</font>
            <img src="../../image/down-arrow.png" width="20px" class="catalogBtnUp">
        </div>
        <!-- 格式開發區塊 start -->
        <!-- <div id='Message_1' class='ChatMessage ChatMessageLeft animate__animated animate__fadeInLeft'>
                <div class="MessageText_Satis">
                    <div class="satisfactionBar animate__animated animate__fadeInUp">
                        <image src="../../image/like.png" class="satisfactionBtn"
                            onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','1');"></image>
                        <image src="../../image/ok.png" class="satisfactionBtn"
                            onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','0');"></image>
                        <image src="../../image/dislike.png" class="satisfactionBtn"
                            onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','-1');"></image>
                        <image src="../../image/copy_1.png" class="satisfactionBtn"
                            onclick="ChatEvent.onCopyClick(this,'{copyString}');"></image>
                    </div>
                    <div class="MessageText" style="width: 95%;">
                        <swiper-container class="mySwiper" pagination="true" pagination-clickable="true"
                            navigation="false" space-between="30" loop="true" slides-per-view="auto" effect="coverflow">
                            <swiper-slide>
                                <div class="card" style="width: 100%">
                                    <img class="QbiCardImage"
                                        src="https://qbi.qbiai.com:39622/gateway/openapi/web/file/download?y1gnjPmQAe2dJ84L3K4FihWuF4opIGUN3XNNXCWayNhm3vY6E75mA1QLX6iR9ixIA5vzppk11fgIuxQlvWMJmwFkOe2divp0%2FgIMueQ2m7T5vgJzIDqEIwu0ZttFQMbQD2fWnQ1S2fp6doFwhLD9ihcUHW%2BX%2BYX3h1gQKRum9Cz93WMklNqw"
                                        alt="Card image cap">
                                    <div class="card-body">
                                        <h5 class="card-title">以下是建議的答案</h5>
                                        <p class="card-text"></p>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">Cras justo odio</li>
                                            <li class="list-group-item">Dapibus ac facilisis in</li>
                                            <li class="list-group-item">Vestibulum at eros</li>
                                        </ul>
                                    </div>
                                </div>
                            </swiper-slide>
                        </swiper-container>
                    </div>
                </div>
            </div> -->
        <!-- 格式開發區塊 end -->

        <div id="messageBox" class="messageBox">
            <div id="messageList" class="messageList">
            </div>
            <div id="ToolZone" style='display:none;'>
                <div id=QuickReply_Container class='swiper'>
                    <div class='swiper-wrapper cardsWrapper' id=QuickReply>
                    </div>
                </div>
            </div>
        </div>
        <div id="sendMessage" class="sendMessage">
            <div class="container">
                <div class="row">
                    <div v-on:click="openFunctionView" class="col-md-12" align="center" style="cursor: pointer;"
                        v-if="ServerConfig.functionEnable">
                        <span>
                            <font class="functionTitle">{{NowSelectFunction.functionName}}
                            </font>
                            <img src="../../image/main-menu.png" width="20px" class="catalogBtnDown"
                                v-bind:title="T['functionButtonHint']">
                        </span>
                    </div>
                    <div class="SendBox col-md-12">
                        <span class="iconcontrol">
                            <img class="cleanIcon" src="../../image/cleaning.png" v-bind:title="T['createNewChat']"
                                v-on:click="cleanMessageAndShowGreeting()">
                        </span>
                        <div class="sendMessageDiv" align="center" id="sendMessageDiv">
                            <span class="MainsendMessageBox">
                                <textarea class="sendMessageBox" aria-label="With textarea"
                                    v-bind:placeholder="isMicroPhoneTurn ? '' : T['sendMessage']" id="sendMessageBox"
                                    v-model="inputQuestion" onkeypress="Chat.onMessageBoxPress();"
                                    onkeyup="Chat.onMessageBoxUp(this)" style="height: 60px;"></textarea>
                                <span class="tokenText character-count"
                                    style="bottom: -6px ;left: 6px; position: relative;"
                                    v-bind:style="{'color':getTokensCount(inputQuestion)>TOKEN_MAX?'red':'white'} ">{{getTokensCount(inputQuestion)}}/{{TOKEN_MAX}}</span>
                            </span>
                            <span class="message-info-container">
                                <span v-if="QbiCopilotInfo.isSTTEnable && isMicroSendIcon">
                                    <img id="SpeechToTextEndBtn" class="microPhoneIconOn"
                                        v-bind:title="T['SpeechToTextBtnHint']" src="../../image/RecognitionOn.png"
                                        v-on:click="WebSpeechRecognition($event)" v-if="isMicroPhoneTurn">
                                    <img id="SpeechToTextBtn" class="microPhoneIconOff"
                                        v-bind:title="T['SpeechToTextBtnHint']" src="../../image/RecognitionOff.png"
                                        v-on:click="WebSpeechRecognition($event)" v-if="!isMicroPhoneTurn">
                                </span>
                                <img class="sendIcon" v-bind:title="T['sendIconHint']" src="../../image/bot.png"
                                    v-on:click="sendMessage" v-if="!isMicroSendIcon">
                            </span>
                            <span
                                v-if="(usermode=='employee' && QbiCopilotInfo.enableWebSearch)||isWebSearchForceEnable">
                                <img v-if="isKnowledgeFirst" v-on:click="switchKnowledgeMode"
                                    src="../../image/knowledgeOn.png" class="knowledgeIconOn"
                                    v-bind:title="T['insideKnowledgeButtonHint']">
                                <img v-if="!isKnowledgeFirst" v-on:click="switchKnowledgeMode"
                                    src="../../image/knowledgeOff.png" class="knowledgeIconOff"
                                    v-bind:title="T['outerKnowledgeButtonHint']">
                            </span>
                            <span class="audio-wave" align="center" id="siri-container">
                                
                            </span>
                        </div>

                    </div>
                </div>

            </div>
        </div>

        <!-- Modal Function任務切換-->
        <div class="modal fade" id="functionView" tabindex="-1" role="dialog" aria-labelledby="functionViewTitle"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="functionViewTitle">{{T['functionTask']}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked"
                                v-model="IS_CLEAR_TASK">
                            <label class="form-check-label" for="flexCheckChecked">
                                {{T['clearTaskInfo']}}
                            </label>
                        </div>
                        <br>
                        <div class="catalogItems">
                            <button type="button" class="btn btn-light btn-sm btn-block catalogItem"
                                v-for="(item, index) in FunctionItems" v-on:click="choseFunction(item)"
                                v-bind:title="item.functionName">
                                {{item.functionName}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal 單選知識-->
        <div class="modal fade" id="KMSelectView" tabindex="-1" role="dialog" aria-labelledby="KMSelectViewTitle"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="KMSelectViewTitle">{{T['catalog']}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <img src="../../image/position.png" width="15px" style="margin-right: 5px;">
                                <li class="breadcrumb-item" v-for="(item, index) in CatalogSelects">
                                    <a class="catalogSelect" v-on:click="doCataLogSelect(item)">{{ item.FName | truncate
                                        }}</a>
                                </li>
                            </ol>
                        </nav>
                        <div class="catalogItems" v-if="CatalogItems && CatalogSelects.length < 4">
                            <button type="button" style="text-align:left"
                                class="btn btn-light btn-sm btn-block catalogItem" v-for="(item, index) in CatalogItems"
                                v-on:click="choseCatalog(item)" v-bind:title="item.FName" v-if="item.FId !== 'all'">
                                <img class="catalogfolder" src="../../image/folder.png" width="20px"
                                    style="margin-right: 5px;">{{ item.FName
                                | truncate }}
                            </button>
                        </div>
                        <div class="catalogItems" v-if="!CatalogItems">
                            <i v-if="CatalogSelects.length>=4" style="margin: 20%">{{T['catalogNotice']}}</i>
                            <i v-if="CatalogSelects.length< 4" style="margin: 30%">{{T['catalogNone']}}</i>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="text-right">
                            <button type="button" class="btn btn-light btn-sm btn-block catalogItem"
                                v-on:click="_selectCataLog()">
                                {{T['OK']}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</body>

</html>