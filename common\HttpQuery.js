var HttpQuery = {
  enableDebugMode: false,
  enableShowLoading: false,
  Parser: class {
    constructor(...targetKeys) {
      this.result = { key: null };
      this.setKeys = new Set(targetKeys);
      this.stateCurrent = "initial";
      this.stateEscapeNext = false;
      this.stateInsideString = false;
      this.stateIsTargetValue = false;
      this.stateValueStarted = false;
      this.stringCurrentKey = "";
    }
    processChunk(chunk) {
      this.result = { key: null };
      for (let loop = 0; loop < chunk.length; loop++) {
        const char = chunk[loop];
        if (this.stateEscapeNext) {
          this.stateEscapeNext = false;
          if (this.stateIsTargetValue) {
            this.stateValueStarted = true;
            this.result = { key: this.stringCurrentKey, state: "starting" };
          }
          continue;
        }
        if (char === "\\") {
          this.stateEscapeNext = true;
        }
        if (char === '"' && !this.stateEscapeNext) {
          this.stateInsideString = !this.stateInsideString;
          if (this.stateInsideString && this.stateCurrent === "initial") {
            this.stateCurrent = "key";
            this.stringCurrentKey = "";
          } else if (!this.stateInsideString && this.stateCurrent === "key") {
            this.stateCurrent = "afterKey";
          } else if (this.stateInsideString && this.stateCurrent === "value" && this.setKeys.has(this.stringCurrentKey)) {
            this.stateIsTargetValue = true;
            this.stateValueStarted = false;
            this.result = { key: null };
          } else if (!this.stateInsideString && this.stateCurrent === "value" && this.stateIsTargetValue) {
            this.result = { key: this.stringCurrentKey, state: "ended" };
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
          }
          continue;
        }
        if (this.stateInsideString && this.stateCurrent === "key") {
          this.stringCurrentKey += char;
        }
        if (!this.stateInsideString) {
          if (char === "{") {
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
          } else if (char === "}") {
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
          } else if (char === "[") {
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
          } else if (char === "]") {
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
          } else if (char === ":" && this.stateCurrent === "afterKey") {
            this.stateCurrent = "value";
          } else if (char === "," && !this.stateInsideString) {
            this.stateCurrent = "initial";
            this.stringCurrentKey = "";
            this.stateIsTargetValue = false;
            this.stateValueStarted = false;
          }
        } else if (this.stateIsTargetValue && !this.stateValueStarted) {
          this.stateValueStarted = true;
          this.result = { key: this.stringCurrentKey, state: "starting" };
        } else if (this.stateIsTargetValue && this.stateValueStarted) {
          this.result = { key: this.stringCurrentKey, state: "starting" };
        }
      }
      return this.result;
    }
  },
  mather(topN, chunkId ,count) {
    let returnValue = null;
    try {
      if (HttpQuery.enableDebugMode) console.log("[stream] mather[" + chunkId + "]");
      const item = topN.find((entry) => entry.metadata.chunkId === chunkId);
      if (item) {
        const match = {
          id: item.metadata.sourceId,
          knowledgeId: item.metadata.knowledgeId,
          count: count,
          content: item.metadata.sourceName,
          page_content: item.page_content,
          type: item.metadata.type,
        };
        returnValue = match;
        if (HttpQuery.enableDebugMode) console.log("[stream] mather[" + chunkId + "]", returnValue);
      }
    } catch (e) {
      console.warn(e);
    }
    return returnValue;
  },
  stripHTML(content) {
    return content
      .replace(/<[^>]+>/g, "")
      .replace(/\n/g, " ")
      .replace(/\t/g, " ");
  },
  streamController() {
    let textQueue = [];
    let countCurrentMessage;
    let stateIsProcessing = false;
    let statePendingBackslash = false;
    let htmlDataSource = null;
    const enqueueText = (text, showContent, isFirstChunk) => {
      if (text) {
        for (let char of text) {
          textQueue.push(char);
          if(isFirstChunk){
            showContent.content =showContent.content.charAt(0)
          }
          processQueue(showContent, isFirstChunk);
        }
      }
    };
    const textQueueClean = () => {
      textQueue=[];
    };
    const setDataSource = (dataSource) => {
      if (dataSource && !!dataSource && String(dataSource).trim().length > 0) {
        this.htmlDataSource = dataSource;
      } else {
        this.htmlDataSource = null;
      }
    };
    const setInnnerDataSource = (innerSource) => {
      if (innerSource && !!innerSource && innerSource.length > 0) {
        this.innnerDataSource = innerSource;
      } else {
        this.innnerDataSource = null;
      }
    };
    const setDataIsReady = (dataIsReady) => {
      this.dataIsReady = dataIsReady;
    };
    const setDataIsRender = (dataIsRender) => {
      this.dataIsRender = dataIsRender;
    };
    const setIsKmNoAnswer = (isKmNoAnswer) => {
      this.isKmNoAnswer = isKmNoAnswer;
    };
    const getDataSource = (chunkIdList,args,stringMessageId,inputQuestion) => {
      this.isKmNoAnswer = false;
      this.args = args;
      if(chunkIdList.length == 0){
        ActionManager.isDoAddHistory = false;
        this.isKmNoAnswer = true;
        this.dataIsReady = true;
        return {source:null,innerSource:[]};
      }
      let index = 1;
      let result = "";
      let innerSource = [];
      let template = `
      <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{source}','{type}', '{mark}')">[{count}] {content}</a>
      <br/>`;
      let templateDownload = `
      <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{source}','{type}')"><img src="../../image/file.png" width="15px"></a>`;
      let templateInnerSource = "";
      for(let chunkId of chunkIdList){
        const item = HttpQuery.mather(args.topN, chunkId,index);
        let resultItem = template;
        resultItem = resultItem.replace('{count}', item.count);
        if ('file' == item.type || 'document' === item.type) {
            if ('file' == item.type) {
                resultItem = resultItem.replaceAll('{id}', item.knowledgeId);
                resultItem = resultItem.replace('{type}', 'km'); //點選開啟知識預覽
            } else if ('document' == item.type) {
                resultItem = resultItem.replaceAll('{id}', item.id);
                resultItem = resultItem.replace('{type}', 'document'); //點選開啟知識預覽
            }
            let download = templateDownload;
            download = download.replaceAll('{id}', item.id);
            download = download.replace('{source}', item.content);
            download = download.replace('{type}', item.type);
            resultItem = resultItem.replaceAll('{source}', item.content);
            templateInnerSource = resultItem.replaceAll('{content}',"");
            resultItem = resultItem.replaceAll('{content}', item.content + download);
        } else {
            resultItem = resultItem.replaceAll('{id}', item.knowledgeId);
            resultItem = resultItem.replace('{type}', 'km'); //點選開啟知識預覽
            resultItem = resultItem.replaceAll('{source}', item.content);
            templateInnerSource = resultItem.replaceAll('{content}',"");
            resultItem = resultItem.replaceAll('{content}', item.content);
            resultItem = resultItem.replace('{mark}', escape(item.page_content))

        }
        index++;
        const innerSourceItem = {
          answer: Chat.Chat_answer.answers.find(item=>item.chunkId == chunkId).answer.slice(3),
          content: templateInnerSource,
        }
        innerSource.push(innerSourceItem);
        result += resultItem;
      }
      const source = `
      <div style="padding-top: 10px">
        <font style="color: rgb(0, 88, 176)">${Text.dataSourceText}</font>
        <div id="source">${result}</div>
        ${MessageText.template_ViewMore.replaceAll("{messageId}",stringMessageId).replace("{inputQuestion}",inputQuestion).replace("{viewMore}",Text.viewMore).replace(/\r?\n/g, "")}
        ${MessageText.template_gptAlert.replace("{gptAlert}",Text.gptAlert).replace(/\r?\n/g, "")}
      </div>`;
      this.dataIsReady = true;
      return {source:source,innerSource:innerSource};
    };
    const processDataIsDone = () => {
      const interval = setInterval(() => {
        if (textQueue.length === 0 && this.dataIsReady || this.isKmNoAnswer) {
          clearInterval(interval);
          this.dataIsRender = true;
          if(this.isKmNoAnswer){
          }
          ChatEvent.doStorageTOPN_KEY(this.args,Chat.Chat_messageId);
          if (this.htmlDataSource && !!this.htmlDataSource && this.htmlDataSource !== null) {
              MessageController.updateMessage(this.countCurrentMessage, this.htmlDataSource.replace(/\r?\n/g, ""), false , false);
              this.htmlDataSource = null;
          }
          if (this.innnerDataSource && !!this.innnerDataSource && this.innnerDataSource !== null) {
              MessageController.updateMessage(this.countCurrentMessage, this.innnerDataSource, false , "innerSource");
              this.innnerDataSource = null;
          }
          Chat.Chat_answerType = "gptText";
          Chat.Chat_dataSource = "llm";
          MessageController.doAddChatHistory().then(() => {
            ChatEvent.saveChatMessage();
            Chat.PageApp.LOCK_SEND = false;
          })
        }
      }, 300);
    }
    const processQueue = (showContent, isFirstChunk) => {
      if (stateIsProcessing) {
        return;
      }
      stateIsProcessing = true;
      const shift_text = textQueue.shift();
      let text = shift_text;
      if (statePendingBackslash) {
        statePendingBackslash = false;
        if (text === "n") {
          text = "<br/>";
        } else {
          text = "\\" + text;
        }
      } else if (text === "\n") {
        text = "<br/>";
      } else if (text === "\\") {
        text = "";
        statePendingBackslash = true;
      }
      if (isFirstChunk) {
        this.countCurrentMessage = MessageController.COUNT;
        const formattedContent = MessageController.text(showContent);
        MessageController.updateMessage(this.countCurrentMessage, formattedContent, true , false);
        MessageController.updateMessage(this.countCurrentMessage, text, false , false);
      } else {
        if(text?.length > 0){
          MessageController.updateMessage(this.countCurrentMessage, text, false , false);
        }
      }
      setTimeout(() => {
        stateIsProcessing = false;
        const chatBox = document.getElementById("messageList");
        chatBox.scrollTop = chatBox.scrollHeight;
        if(textQueue.length > 0){
          processQueue();
        }
      }, 1);
    };
    return { textQueueClean ,enqueueText, setDataSource, getDataSource, setInnnerDataSource, setDataIsReady, setDataIsRender, processDataIsDone, setIsKmNoAnswer};
  },
  doRequest(url, args, processDone, processError) {
      let isShowLoading = !!parent.Main;
      if (isShowLoading) {
        if (HttpQuery.enableShowLoading) parent.Main.showLoading(true, 250);
      }
      let data = JSON.stringify(args);
      let km = JSON.parse(JSON.stringify(args));
      $.ajax({
        url: Config.ECP_URL + url,
        type: "post",
        data: data,
        contentType: "application/json",
        async: true,
        success: function (data, textStatus, request) {
          if (isShowLoading) {
            if (HttpQuery.enableShowLoading) parent.Main.showLoading(true, 250);
          }
          processDone(data);
        },
        error: function (jqXHR, textStatus, errorThrown) {
          if (isShowLoading) {
            if (HttpQuery.enableShowLoading) parent.Main.showLoading(true, 250);
          }
          Swal.fire({ icon: "error", title: Text["errorTitle"], text: Text["errorContent"], confirmButtonColor: "#8ba8d9" });
          processError();
        },
      });
  },
  async streamKM(args, url, responseFromFetch = null) {
    const listTopN = args.topN;
    const objectParser = new HttpQuery.Parser("matched","answer", "chunkId");
    const objectStreamController = HttpQuery.streamController();
    objectStreamController.setDataSource(null);
    objectStreamController.setDataIsRender(false);
    objectStreamController.setDataIsReady(false);
    objectStreamController.setIsKmNoAnswer(false);
    let countCurrentMessage = null;
    let stateAnswered = false;
    let stateIsFirstChunk = true;
    let stringAnswerChunkId = null;
    let stringCurrentChunkIdList = [];
    let stringCurrentChunkId = null;
    let stringFullResponse = "";
    let stringMessageId ="";
    let stringMatched = "";
    try {
      let response = responseFromFetch;
      this.isWebSearchEnable = (Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE;
      stringMessageId = response.headers.get('Message-Id');
      const objectReader = response.body.getReader();
      const objectDecoder = new TextDecoder("utf-8");
      let stringBuffer = "";
      while (true) {
        let { done, value } = await objectReader.read();
        if (done) {
          if (stringFullResponse && !!stringFullResponse && stringMatched.includes("No")) {
            if (HttpQuery.enableDebugMode) console.log("[stream]", JSON.stringify(listTopN, "", 4), "\n--------\n", stringFullResponse, "\n--------\n");
            
            ActionManager.isDoAddHistory = true;
            Chat.Chat_answer = JSON.parse(MessageController.cleanJsonBlock(stringFullResponse));
            Chat.Chat_messageId = stringMessageId;
            Chat.Chat_answer.answers.forEach(answersitem => {
                const chunk = args.topN.find(item=>item.metadata.chunkId == answersitem.chunkId);
                if(chunk){
                  Chat.Chat_kmIds.push(chunk.metadata.knowledgeId);
                }
            });
            const message = CommonUtil.stripHTML(Chat.Chat_answer?.answers.map(item => item.answer).join(''),true);
            ActionFunction.HISTORY_RECORD_AI.message = message;
            MessageController.TotalMessage_Synthesis = message; 
            //自動播音判斷
            if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis && stringCurrentChunkIdList.length > 0) {
              if (!((this.isWebSearchEnable)&&stringCurrentChunkIdList.length == 0)) {
                WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
              }
            }
            
            const data = objectStreamController.getDataSource(stringCurrentChunkIdList,args,stringMessageId,args.inputQuestion);
            objectStreamController.processDataIsDone();
            MessageController.updateMessage(this.countCurrentMessage, MessageController.TotalMessage_Synthesis, false , "handleSpeakMessageVolume");
            objectStreamController.setInnnerDataSource(data.innerSource);
            objectStreamController.setDataSource(data.source);
            if (stateAnswered === false) {
              for (let char of stringFullResponse) {
                const showContent = { needSatisfaction: stateIsFirstChunk, needSpeechSynthesis: true, content: char, messageId: stringMessageId };
                objectStreamController.enqueueText(char, showContent, stateIsFirstChunk);
                stateIsFirstChunk = stateIsFirstChunk === true ? false : stateIsFirstChunk;
              }
            }
          }
          break;
        }
        stringBuffer += objectDecoder.decode(value, { stream: true });
        const lines = stringBuffer.split("\n");
        stringBuffer = lines.pop();
        for (const line of lines) {
          if (line.startsWith("data:")) {
            const stringData = line.substring("data:".length).trim();
            try {
              const chunk = JSON.parse(stringData);
              if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta && chunk.choices[0].delta.content) {
                let contentPart = chunk.choices[0].delta.content;
                let formattedContent = contentPart;
                const showContent = { needSatisfaction: stateIsFirstChunk, needSpeechSynthesis: true, content: " ", messageId: stringMessageId };
                stringFullResponse += contentPart;
                formattedContent = MessageController.escapeHTML(contentPart).replace(/\n/g, "<br/>");
                const stateParsing = objectParser.processChunk(contentPart);
                if (stateParsing && !!stateParsing && stateParsing.key && !!stateParsing.key && stateParsing.key !== null) {
                  switch (String(stateParsing.key).trim()) {
                    case "matched":
                      if (stateParsing.state && !!stateParsing.state) {
                        if (stateParsing.state !== "ended") {
                          stringMatched += contentPart;
                        } else {
                          console.log("stringMatche[" + stringMatched + "]");
                          if(stringMatched.includes("No")){
                            if (this.isWebSearchEnable) {
                              ActionManager.isDoAddHistory = true;
                              objectStreamController.textQueueClean();
                              MessageController.doParseGoogleSearchMessage(args, null);
                              MessageController.updateMessage(this.countCurrentMessage, {}, false , "isKmNoAnswer");
                              break;
                            }
                          }
                        }
                      }
                    break;
                    case "answer":
                      if (stateParsing.state && !!stateParsing.state && stateParsing.state !== "ended") {
                        if (stringMatched == "Yes" || (stringMatched == "No"&& !this.isWebSearchEnable)) {
                         objectStreamController.enqueueText(contentPart, showContent, stateIsFirstChunk);
                         stateIsFirstChunk = stateIsFirstChunk === true ? false : stateIsFirstChunk;
                        }
                      }
                      stateAnswered = true;
                      break;
                    case "chunkId":
                      if (stateParsing.state && !!stateParsing.state && stateParsing.state === "ended") {
                        stringAnswerChunkId = stringCurrentChunkId;
                        if(stringAnswerChunkId!=null && stringAnswerChunkId!=="")stringCurrentChunkIdList.push(stringAnswerChunkId);
                        console.log("[stream]", "chunkId", stringAnswerChunkId);
                        stringCurrentChunkId = null;
                      } else {
                        stringCurrentChunkId = stringCurrentChunkId != null ? stringCurrentChunkId + contentPart : contentPart;
                      }
                      break;
                  }
                }
              }
            } catch (e) {
              console.warn(e, line);
            }
          }
        }
      }
    } catch (e) {
      console.warn(e);
      ChatEvent._showError();
      Chat.PageApp.LOCK_SEND = false;
    }
  },
  async doRequestFetch(url, args) {
    try{
      // fortify-disable-next-line CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");
      const response = await fetch(Config.ECP_URL + url, {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-CSRF-Token": csrfToken },
        body: JSON.stringify(args),
      });
      if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("text/event-stream")) {
        await HttpQuery.streamKM(args, url, response);
        return { __stream: true };
      }
      return await response.json();
    }catch(e){
      console.warn(e);
      ChatEvent._showError();
      Chat.PageApp.LOCK_SEND = false;
    }
  }
};
