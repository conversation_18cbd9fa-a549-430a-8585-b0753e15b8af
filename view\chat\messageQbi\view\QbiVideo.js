var QbiVideo = {
    create(answer) {
        let html="";
        if (!!~answer.originalContentUrl.toLowerCase().indexOf("https://www.youtube.com")) {
            html = "<iframe class='videoIframe' src='" + answer.originalContentUrl + "'>" + "</iframe>";
        } else {
            html =
                "<video controls class='videoIframe' title=" +
                Text['VideoType'] +
                (answer.thumbnailUrl ? " poster='" + answer.thumbnailUrl + "'" : "") +
                ">" +
                "<source src='" +
                answer.originalContentUrl +
                "' type='video/mp4'>" +
                "</video>";
        }
        return html;
    }
}