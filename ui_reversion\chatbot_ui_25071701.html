<!DOCTYPE html>
<html lang="zh-TW">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>知識對話小幫手</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      font-family: "Noto Sans TC", Arial, sans-serif;
      background-color: #eef2f7;
      display: flex;
      height: 100vh;
    }

    .crm-area {
      flex: 1;
      background: #f3f6fb;
    }

    .history-panel {
      width: 229px;
      height: 100%;
      background-color: #ffffff;
      border-left: 1px solid #c8d2e0;
      border-right: 1px solid #c8d2e0;
      display: flex;
      flex-direction: column;
    }

    .history-header {
      height: 41px;
      background-color: #1f4379;
      color: white;
      font-size: 14px;
      display: flex;
      align-items: center;
      padding-left: 12px;
    }

    .history-content {
      flex: 1;
      overflow-y: auto;
      font-size: 14px;
    }

    .history-item {
      padding: 10px 12px;
      border-bottom: 1px solid #e5e5e5;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
    }

    .chatbot-container {
      width: 460px;
      height: 100%;
      background-color: #ffffff;
      border-left: 1px solid #c8d2e0;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .chatbot-header {
      height: 41px;
      background-color: #1f4379;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      color: white;
      font-size: 14px;
    }

    .chatbot-header .chatbot-menus {
      display: flex;
      align-items: center;
    }

    .chatbot-header .chatbot-menus span {
      padding-left: 10px;
    }

    .chat-body {
      flex: 1;
      overflow-y: auto;
      padding: 12px;
    }

    .chat-message {
      margin-bottom: 16px;
    }

    .chat-bubble-group {
      padding-bottom: 30px;
    }

    .chat-bubble {
      display: inline-block;
      max-width: 60%;
      padding: 10px 12px;
      border-radius: 10px;
      font-size: 14px;
      word-break: break-word;
    }

    .chat-bubble.bot {
      float: left;
      background-color: #f1f1f1;
      text-align: left;
    }

    .chat-time.bot {
      float: left;
      font-size: 10px;
      color: #888;
      margin-top: 25px;
    }

    .chat-bubble.user {
      float: right;
      background-color: #d3ecff;
      margin-left: auto;
      text-align: right;
    }

    .chat-time.user {
      float: right;
      font-size: 10px;
      color: #888;
      margin-top: 25px;
    }

    .chat-input {
      border-top: 1px solid #ccc;
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .chat-input-row1 {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chat-input-row1 input {
      flex: 1;
      padding: 8px 10px;
      font-size: 14px;
      border: none;
      outline: none;
    }

    .chat-input-row1 .input-right {
      display: flex;
      gap: 8px;
      align-items: center;
      color: #888;
      font-size: 12px;
    }

    .chat-input-row2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 6px;
      padding: 6px 8px;
    }

    .chat-actions-left {
      display: flex;
      gap: 12px;
    }

    .chat-actions-left img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }

    .chat-actions-right {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .chat-actions-right select {
      border: none;
      background: transparent;
      font-size: 14px;
      outline: none;
    }

    .chat-actions-right button {
      background: #3182f6;
      border: none;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      color: white;
      font-size: 18px;
      cursor: pointer;
    }

    @media screen and (max-width: 768px) {
      .history-panel {
        display: none;
      }

      .chatbot-container {
        width: 100%;
      }
    }

    .chatbot-v-align {
      display: flex;
      justify-content: center;
    }

    .chatbot-history-icon {
      padding-right: 8px;
    }
  </style>
</head>

<body>
  <div class="crm-area"></div>
  <div class="history-panel">
    <div class="history-header">
      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="1" width="19" height="19" rx="2.5" stroke="white" />
        <path d="M7.5 1.5L7.5 19.5" stroke="white" />
        <path d="M2.5 4.5H5.5" stroke="white" stroke-linecap="round" />
        <path d="M2.5 7.5H5.5" stroke="white" stroke-linecap="round" />
      </svg>
    </div>
    <div class="history-content">
      <div class="history-item">
        <span>今天：切換語言背景問題</span>
        <span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="3" cy="10" r="2" fill="black" />
            <circle cx="10" cy="10" r="2" fill="black" />
            <circle cx="17" cy="10" r="2" fill="black" />
          </svg>
        </span>
      </div>
      <div class="history-item">
        <span>昨天：HTML語法化解析</span>
        <span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="3" cy="10" r="2" fill="black" />
            <circle cx="10" cy="10" r="2" fill="black" />
            <circle cx="17" cy="10" r="2" fill="black" />
          </svg>
        </span>
      </div>
      <div class="history-item">
        <span>過去七天：XSS濫用修補建議</span>
        <span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="3" cy="10" r="2" fill="black" />
            <circle cx="10" cy="10" r="2" fill="black" />
            <circle cx="17" cy="10" r="2" fill="black" />
          </svg>
        </span>
      </div>
    </div>
  </div>
  <div class="chatbot-container">
    <div class="chatbot-header">
      <div class="chatbot-v-align">
        <span class="chatbot-history-icon">
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.5" y="1" width="19" height="19" rx="2.5" stroke="white" />
            <path d="M7.5 1.5L7.5 19.5" stroke="white" />
            <path d="M2.5 4.5H5.5" stroke="white" stroke-linecap="round" />
            <path d="M2.5 7.5H5.5" stroke="white" stroke-linecap="round" />
          </svg>
        </span>
        <span>知識對話小幫手</span>
      </div>
      <div class="chatbot-menus">
        <span>
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="path-1-inside-1_23_513" fill="white">
              <path d="M0 8.48V7.5C0 2.5 2 0.5 7 0.5H13C18 0.5 20 2.5 20 7.5V13.5C20 18.5 18 20.5 13 20.5H12" />
            </mask>
            <path d="M-1 8.48C-1 9.03228 -0.552285 9.48 0 9.48C0.552285 9.48 1 9.03228 1 8.48H-1ZM12 19.5C11.4477 19.5 11 19.9477 11 20.5C11 21.0523 11.4477 21.5 12 21.5V19.5ZM1 8.48V7.5H-1V8.48H1ZM1 7.5C1 5.09863 1.4864 3.67782 2.33211 2.83211C3.17782 1.9864 4.59863 1.5 7 1.5V-0.5C4.40137 -0.5 2.32218 0.0136016 0.917893 1.41789C-0.486398 2.82218 -1 4.90137 -1 7.5H1ZM7 1.5H13V-0.5H7V1.5ZM13 1.5C15.4014 1.5 16.8222 1.9864 17.6679 2.83211C18.5136 3.67782 19 5.09863 19 7.5H21C21 4.90137 20.4864 2.82218 19.0821 1.41789C17.6778 0.0136016 15.5986 -0.5 13 -0.5V1.5ZM19 7.5V13.5H21V7.5H19ZM19 13.5C19 15.9014 18.5136 17.3222 17.6679 18.1679C16.8222 19.0136 15.4014 19.5 13 19.5V21.5C15.5986 21.5 17.6778 20.9864 19.0821 19.5821C20.4864 18.1778 21 16.0986 21 13.5H19ZM13 19.5H12V21.5H13V19.5Z" fill="white" mask="url(#path-1-inside-1_23_513)" />
            <path d="M10 9.5L16 3.5H11.1976" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M16 3.5V8.5" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M3.81714 11.3333H6.51636C7.59204 11.3333 8.21518 11.5517 8.58179 11.9183C8.94839 12.2849 9.16675 12.908 9.16675 13.9837V16.6829C9.16675 17.7586 8.94839 18.3817 8.58179 18.7484C8.21518 19.115 7.59204 19.3333 6.51636 19.3333H3.81714C2.74145 19.3333 2.11831 19.115 1.75171 18.7484C1.3851 18.3817 1.16675 17.7586 1.16675 16.6829V13.9837L1.17651 13.6009C1.22328 12.7559 1.43091 12.2391 1.75171 11.9183C2.11831 11.5517 2.74145 11.3333 3.81714 11.3333Z" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </span>
        <span>
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 13.2C1 16.683 3.817 19.5 7.3 19.5L6.355 17.925" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M19 7.8C19 4.317 16.183 1.5 12.7 1.5L13.645 3.075" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M2 12.1452L6.19048 1.5L8.89401 9.30645M10 12.5L8.89401 9.30645M3.14286 9.30645C3.44762 9.30645 7.10394 9.30645 8.89401 9.30645" stroke="white" stroke-width="1.5" stroke-linejoin="round" />
            <path d="M13 19.2419L16.1429 11.5L18.1705 17.1774M19 19.5L18.1705 17.1774M13.8571 17.1774C14.0857 17.1774 16.828 17.1774 18.1705 17.1774" stroke="white" stroke-width="1.5" stroke-linejoin="round" />
          </svg>
        </span>
        <span>
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="3" cy="10.5" r="2" fill="white" />
            <circle cx="10" cy="10.5" r="2" fill="white" />
            <circle cx="17" cy="10.5" r="2" fill="white" />
          </svg>
        </span>
        <span>
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_438_3993)">
              <circle cx="12" cy="12.5" r="10" fill="white" />
            </g>
            <path d="M17.3791 6.58217C17.5731 6.45414 17.8372 6.4758 18.008 6.64662C18.1787 6.81743 18.2004 7.08147 18.0724 7.27553L18.008 7.35365L12.7834 12.5773L18.0089 17.8019C18.2042 17.9971 18.204 18.3137 18.0089 18.5089C17.8137 18.7042 17.4972 18.7042 17.3019 18.5089L12.0773 13.2843L6.85367 18.5089C6.65841 18.7042 6.34189 18.7042 6.14663 18.5089C5.95151 18.3136 5.95142 17.9971 6.14663 17.8019L11.3693 12.5773L6.14663 7.35463L6.08218 7.2765C5.95412 7.08245 5.97581 6.81841 6.14663 6.6476C6.31747 6.47688 6.58151 6.45511 6.77554 6.58314L6.85367 6.6476L12.0763 11.8703L17.3009 6.64662L17.3791 6.58217Z" fill="black" />
            <defs>
              <filter id="filter0_d_438_3993" x="0" y="0.5" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset />
                <feGaussianBlur stdDeviation="1" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_3993" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_3993" result="shape" />
              </filter>
            </defs>
          </svg>
        </span>
      </div>
    </div>
    <div class="chat-body">
      <div class="chat-message">
        <div class="chat-bubble-group">
          <div class="chat-bubble bot">Hey, Lisa! How’s your day going?</div>
          <div class="chat-time bot">mm:ss</div>
        </div>
      </div>
      <div class="chat-message">
        <div class="chat-bubble-group">
          <div class="chat-bubble user">Hey, Lisa! How’s your day going?</div>
          <div class="chat-time user">mm:ss</div>
        </div>
      </div>
    </div>
    <div class="chat-input">
      <div class="chat-input-row1">
        <input type="text" placeholder="想要問什麼呢？" maxlength="1000" />
        <div class="input-right">0/1000 <img src="https://cdn-icons-png.flaticon.com/512/724/724715.png" width="20" /></div>
      </div>
      <div class="chat-input-row2">
        <div class="chat-actions-left">
          <img src="https://cdn-icons-png.flaticon.com/512/25/25694.png" alt="書籤" title="收藏" />
          <img src="https://cdn-icons-png.flaticon.com/512/709/709496.png" alt="書本" title="知識庫" />
        </div>
        <div class="chat-actions-right">
          <select>
            <option>知識庫</option>
          </select>
          <button title="送出">↑</button>
        </div>
      </div>
    </div>
  </div>
</body>

</html>