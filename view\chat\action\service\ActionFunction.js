var ActionFunction = {

    DEFAULT_KM_ANSWER: 'get_km_answer',
    DEFAULT_FUNCTION: 'defaultOption',
    ALL_FUNCTIONS: [],
    TASK_LIST: [],
    HISTORY_RECORD: { messages: [{ type: "human", message: "" }, { type: "ai", message: "" }] },
    HISTORY_RECORD_HUMAN: { type: "human", message: "" },
    HISTORY_RECORD_AI: { type: "ai", message: "" },
    TASK_Switch: false,

    execute(args) {
        let chatId = args.chatId;
        let inputQuestion = args.inputQuestion;
        Chat.Chat_inputQuestion = args.inputQuestion;
        ActionFunction.HISTORY_RECORD_HUMAN.message = Chat.Chat_inputQuestion;
        ActionFunction.HISTORY_RECORD.messages[0] = ActionFunction.HISTORY_RECORD_HUMAN
        let login = args.login;
        MessageController.TotalMessage_Synthesis = "";

        let functionTarget = [];
        if (ActionFunction.DEFAULT_FUNCTION !== Chat.PageApp.NowSelectFunction.name) {
            functionTarget.push(Chat.PageApp.NowSelectFunction);
        } else {
            functionTarget = ActionFunction.ALL_FUNCTIONS;
        }

        if (ActionFunction.DEFAULT_KM_ANSWER === Chat.PageApp.NowSelectFunction.name) {
            let answerMode = Chat.PageApp.ServerConfig.answerMode;
            if (ActionCopilot.ANSWER_GPT_AND_KM == answerMode || ActionCopilot.ANSWER_GPT_PLUS_EASY == answerMode) {
                ActionManager.do_QbiBotPlus(args);
            } else {
                ActionManager.get_km_answer(args);
            }
            return;
        }

        Chat.PageApp.NoticeContent = {
            noticeId: CommonUtil.getRandomUuid(),
            content: Text['answering']
        }
        MessageController.doAddMessage(MessageController.notice(Chat.PageApp.NoticeContent), MessageController.LEFT);

        HttpQuery.doRequest('/openapi/gpt/askFunction', {
            ask_question: inputQuestion,
            chatId: chatId,
            functions: functionTarget
        }, function (result) {
            try {
                if (result.success) {
                    ActionFunction.TASK_Switch = false;
                    ActionManager.isDoAddHistory = false;
                    if (ActionFunction.TASK_LIST.length == 0 &&  result.hasOwnProperty('task') ) {
                        ActionFunction.TASK_LIST = result.task;
                    } else if (result.hasOwnProperty('task') && result.task.length > 0) {
                        let isClear = true;
                        for (i = 0; result.task.length > i; i++) {
                            if (ActionFunction.TASK_LIST.includes(result.task[i])) {
                                isClear = false;
                                break;
                            }
                        }
                        ActionFunction.TASK_LIST = result.task;
                        if (isClear) {
                            ActionFunction.TASK_Switch = true;
                        }
                    }
                    if (ActionFunction.TASK_Switch) {
                        MessageController.cleanChatMemory().then(() => {
                            ActionFunction.functionFlow(result, args)
                        });
                    } else {
                        ActionFunction.functionFlow(result, args);
                    }
                } else {
                    ChatEvent._showError();
                }
            } catch (e) {
                console.error('[ActionFunction-execute] e:' + e);
                ChatEvent._showError();
            } finally {
                if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                    WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
                }
            }
        });
    },

    functionFlow(result, args) {
        return new Promise((resolve, reject) => {
            try {
                Chat.Chat_dataSource = "function";
                let isFunctionCall = false;
                //例外-當finish_reason不等於function_call但於stop且result.message.function_call.name等於DEFAULT_KM_ANSWER時，視為function_call
                isFunctionCall = result.hasOwnProperty('stop') && result.message.hasOwnProperty('function_call') && (result.message.function_call.name == ActionFunction.DEFAULT_KM_ANSWER);

                if (result.hasOwnProperty('finish_reason')) {
                    if ("function_call" === result.finish_reason || isFunctionCall) {
                        //進入QA、KM流程、外部知識，清除任務新增history相關參數
                        ActionFunction.TASK_Switch = false;

                        let answerMode = Chat.PageApp.ServerConfig.answerMode;
                        if (answerMode === ActionCopilot.ANSWER_GPT_AND_KM) {
                            ActionManager.do_QbiBotPlus(args);
                        } else {
                            ActionManager.get_km_answer(args);
                        }
                        resolve();
                        return;
                    } else if ("external_answer" === result.finish_reason) {
                        //進入QA、KM、外部知識流程，清除任務新增history相關參數
                        ActionFunction.TASK_Switch = false;
                        
                        Chat.Chat_dataSource = "web";
                        // 鎖定在外部知識的回應
                        let answer = result.answer;
                        QbiAnswer.doDisplayAnswer(result.messageId, answer,null);
                        resolve();
                        return;
                    } else {
                        // 一般GPT回應結果
                        let botAnswer = result.message.content;
                        ActionFunction.HISTORY_RECORD_AI.message = botAnswer;
                        botAnswer+="{functionGenerateText}"
                        let content = MessageController.text(botAnswer);
                        Chat.Chat_answer = JSON.stringify({ text: botAnswer, type: "text" });
                        Chat.Chat_answerType = "gptText";
                        MessageController.doAddMessage(content, MessageController.LEFT);
                        Chat.PageApp.LOCK_SEND = false;
                        resolve();
                        return;
                    }
                } else {
                    // 最終結果
                    // 由前端執行的Function目前只有 - get_km_answer
                    // ActionFunction._getFunction(ActionFunction.DEFAULT_KM_ANSWER); 
                    let answer = result.response.answer;
                    if (result?.response?.kmAnswer) {
                        let kmAnswer = JSON.parse(result.response.kmAnswer[0].answer);
                        kmAnswer.messageId = result.response.kmAnswerMessageId;
                        kmAnswer.inputQuestion = args.inputQuestion;
                        //GPT專屬格式(from CBM)
                        if (kmAnswer?.mode && kmAnswer.mode === "GPT") {
                        //尚未開發完成
                        // MessageController.doAddMessage(
                        //     MessageGptText.create(kmAnswer),
                        //     MessageController.LEFT
                        // );
                             
                        Chat.Chat_answer = JSON.stringify({ text: Text["taskError"], type: "text" });
                        Chat.Chat_answerType = "text";
                        Chat.Chat_dataSource = "error";
                        MessageController.doAddMessage( MessageController.text(Text["taskError"]),MessageController.LEFT);   
                        Chat.PageApp.LOCK_SEND = false;
                        }
                    }else{
                        if(result?.executeType && !(result?.executeType==="EXECUTE_QBI_ANSWER")){
                            let generalType = result?.executeType
                            QbiAnswer.doDisplayAnswer(result.messageId, answer,generalType);
                        }else{
                            QbiAnswer.doDisplayAnswer(result.messageId, answer,null);
                        };
                    }
                    resolve();
                }
            } catch (e) {
                console.error('[ActionFunction-execute] e:' + e);
                ChatEvent._showError();
            }
        });
    },

    initFunctionList() {
        let info = CommonUtil.getLocalStorage('QbiCopilotInfo');
        if (info.hasOwnProperty('km_functionName')) {
            ActionFunction.DEFAULT_KM_ANSWER = info['km_functionName'];
        }
        HttpQuery.doRequest('/openapi/gpt/getFunctions', {}, function (result) {
            let items = [];
            if (Chat.PageApp.QbiCopilotInfo.enableAutoFunction) {
                let defaultOption = {
                    "functionName": Text['autoDetect'],
                    "name": ActionFunction.DEFAULT_FUNCTION,
                };
                items.push(defaultOption);
            }
            let defaultFunction = {};
            for (let i = 0; i < result.functions.length; i++) {
                if (result.functions[i].display) {
                    items.push(result.functions[i]);
                    if (result.functions[i].name === ActionFunction.DEFAULT_KM_ANSWER) {
                        defaultFunction = result.functions[i];
                    }
                }
            }
            Chat.PageApp.FunctionItems = items;
            Chat.PageApp.NowSelectFunction = Chat.PageApp.QbiCopilotInfo.enableAutoFunction ? items[0] : defaultFunction;
            ActionFunction.ALL_FUNCTIONS = result.functions;
            console.log('[ActionFunction-initFunctionList] function size:' + Chat.PageApp.FunctionItems.length);
        });
    },

    _getFunction(name) {
        Chat.PageApp.FunctionItems.forEach(item => {
            if (name === item.name) {
                Chat.PageApp.NowSelectFunction = item;
            }
        });
    }
}