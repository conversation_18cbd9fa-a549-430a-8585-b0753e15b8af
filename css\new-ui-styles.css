/* 新版 UI 樣式 - 從 chatbot_ui_25071701.html 提取 */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Noto Sans TC", Arial, sans-serif;
  background-color: #eef2f7;
  display: flex;
  height: 100vh;
}

.crm-area {
  flex: 1;
  background: #f3f6fb;
}

.history-panel {
  width: 229px;
  height: 100%;
  background-color: #ffffff;
  border-left: 1px solid #c8d2e0;
  border-right: 1px solid #c8d2e0;
  display: flex;
  flex-direction: column;
}

.history-header {
  height: 41px;
  background-color: #1f4379;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding-left: 12px;
}

.history-content {
  flex: 1;
  overflow-y: auto;
  font-size: 14px;
}

.history-item {
  padding: 10px 12px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.chatbot-container {
  width: 460px;
  height: 100%;
  background-color: #ffffff;
  border-left: 1px solid #c8d2e0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chatbot-header {
  height: 41px;
  background-color: #1f4379;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  color: white;
  font-size: 14px;
}

.chatbot-header .chatbot-menus {
  display: flex;
  align-items: center;
}

.chatbot-header .chatbot-menus span {
  padding-left: 10px;
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.chat-message {
  margin-bottom: 16px;
}

.chat-bubble-group {
  padding-bottom: 30px;
}

.chat-bubble {
  display: inline-block;
  max-width: 60%;
  padding: 10px 12px;
  border-radius: 10px;
  font-size: 14px;
  word-break: break-word;
}

.chat-bubble.bot {
  float: left;
  background-color: #f1f1f1;
  text-align: left;
}

.chat-time.bot {
  float: left;
  font-size: 10px;
  color: #888;
  margin-top: 25px;
}

.chat-bubble.user {
  float: right;
  background-color: #d3ecff;
  margin-left: auto;
  text-align: right;
}

.chat-time.user {
  float: right;
  font-size: 10px;
  color: #888;
  margin-top: 25px;
}

.chat-input {
  border-top: 1px solid #ccc;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.chat-input-row1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-input-row1 input {
  flex: 1;
  padding: 8px 10px;
  font-size: 14px;
  border: none;
  outline: none;
}

.chat-input-row1 .input-right {
  display: flex;
  gap: 8px;
  align-items: center;
  color: #888;
  font-size: 12px;
}

.chat-input-row2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px;
  padding: 6px 8px;
}

.chat-actions-left {
  display: flex;
  gap: 12px;
}

.chat-actions-left img {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.chat-actions-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

.chat-actions-right select {
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
}

.chat-actions-right button {
  background: #3182f6;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  color: white;
  font-size: 18px;
  cursor: pointer;
}

.chatbot-v-align {
  display: flex;
  justify-content: center;
}

.chatbot-history-icon {
  padding-right: 8px;
}

/* 響應式設計 */
@media screen and (max-width: 768px) {
  .history-panel {
    display: none;
  }

  .chatbot-container {
    width: 100%;
  }
}
