var EcpMemberLogin = {
    doLogin() {
        let info = CommonUtil.getLocalStorage('QbiCopilotBaseInfo');
        HttpQuery.doRequest('/openapi/ecp/contact/token/apply', {
            loginName: Login.PageApp.account,
            password: Login.PageApp.pd,
            language: Login.LANGUAGE,
            tenantCode: info.tenantCode
        }, function (result) {
            if (result._header_.success) {
                let userInfoData = { userName: result.contact.name, userId: result.contact.id, mode: 'member', tokenData: result };
                parent.Main.PageApp.UserInfo.userName = userInfoData.userName;
                parent.Main.PageApp.UserInfo.userMode = userInfoData.mode;
                CommonUtil.setLocalStorage('UserInfo', JSON.stringify(userInfoData));
                EcpMemberLogin.doReadyLogin(result.contact.id, result.contact.name, result.tokenId);
            } else {
                Swal.fire({
                    icon: 'info',
                    title: Text['errorLogin'],
                    text: Text['errorLoginContent'],
                    showCloseButton: false,
                    showCancelButton: false,
                    showConfirmButton: true,
                    confirmButtonText: Text['OK'],
                });
            }
        });
    },

    doReadyLogin(userId, name, tokenId) {
        HttpQuery.doRequest('/openapi/copilot/getCopilotInfo', {
            userId: userId,
            tokenId: tokenId,
            mode: 'API'
        }, function (result) {
            CommonUtil.setLocalStorage('QbiCopilotInfo', JSON.stringify(result));
            if (result.enable) {
                parent.Main.PageApp.isLogin = true;
                parent.Main.PageApp.UserInfo.userName = name;
                parent.Main.PageApp.showLogout = true;
                if (result.isReady) {
                    Login._loginSuccess();
                } else {
                    parent.Main.doNotReady();
                }
            }
        });
    }
}