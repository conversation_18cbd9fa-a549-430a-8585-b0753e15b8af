/* MessageGptText */
.MessageGptText {
    display: table-cell;
    width: 95%;
}

.ViewMoreContainer {
    
}

.ViewMoreText {
    float:inline-end;
    padding: 3px ;
    border-radius: 3px ;
}
.hljs, pre, code {
    display: inline-grid;
}

.WordBreakAll{
    word-break: break-all;
}
/* GptRelated */
.GptRelated {
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    padding: 5px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    transition: background-color 0.1s;
}

.GptRelated:hover {
    background-color: rgb(255, 255, 255);
    -webkit-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}

/* MessageText */
.MessageText {
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    padding: 10px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    min-width: 20px;
    max-width: 95%;
}

/* MessageWebSearchText */
.MessageWebSearchText {
    display: table-cell;
    width: 95%;
}


/* --------------滿意度按鈕-------------- */

.satisfactionBtnLike {
    width: 20px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
.satisfactionBtnOk {
    width: 20px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
.satisfactionBtnDislike {
    width: 20px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
.satisfactionBtnCopy {
    width: 20px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
.satisfactionBtnLike:hover {
    width: 25px;
}

.satisfactionBtnOk:hover {
    width: 25px;
}
.satisfactionBtnDislike:hover {
    width: 25px;
}

.satisfactionBtnCopy:hover {
    width: 25px;
}
.grayScaleIcon{
    filter: grayscale(100%);
}
/* --------------滿意度-------------- */

.satisfactionBar {
    position: relative;
    right: 5%;
    float: inline-end;
    padding-bottom: 2px;
}


/* --------------訊息框CSS-------------- */
.MessageText_Container{
    display: table-cell;
    width: 95%;
}
.MessageText_Satis {
    width: 100%;
}

.ChatMessageLeft {
    text-align: left;
}
.ChatMessageLeft .MessageText {
    min-width: 300px;
}

.ChatMessageRight {
    text-align: right;
}

.ChatMessage {
    margin: 10px;
}


/* --------------語音按鈕-------------- */
.SpeakMessageVolume {
    filter: brightness(0.93);
    width: 20px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.SpeakMessageVolume:hover {
    width: 25px;
}