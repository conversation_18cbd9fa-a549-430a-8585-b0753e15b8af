var Main = {

    MODE_USER: 'employee',
    MODE_MEMBER: 'member',

    TimeerCleanList: [],

    doLoad() {
        this.initApp();
        this.initIframePost();
        ThirdPartyTool.includeJS('../../lib/bootstrap/js/bootstrap.min.js');
        ThirdPartyTool.includeCSS('../../lib/bootstrap/css/bootstrap.min.css');
        try {
            if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
                this.goPage(ViewController.PAGE.CHAT);
            } else {
                this.goPage(ViewController.PAGE.LOGIN);
            }
        } catch (e) {
            console.log('[Main-doLoad] default login page.');
            this.goPage(ViewController.PAGE.LOGIN);
        }
        let soure = CommonUtil.getLocalStorage('source');
        if ('teams' == soure) {
            Main.doChangeSize('l');
        } else {
            Main.doChangeSize(Config.COPILOT_SIZE);
        }
    },
    initIframePost() {
        CommonUtil.listen({
            doShowHistoryData: (data) => {
                document.getElementById('mainFrame').contentWindow.Chat.doShowHistoryData(data);
            },
            cleanMessageAndShowGreeting: () => {
                document.getElementById('mainFrame').contentWindow.Chat.cleanMessageAndShowGreeting();
            },
            doUpdateHistoryList: (data) => {
                document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = data;
            },
            doGetHistoryData: (data) => {
                document.getElementById('mainFrame').contentWindow.Chat.doGetHistoryData(data);
            },
        });
    },
    initApp() {
        Main.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                showLogout: false,
                showLeave: false,
                showAutoSpeech: false,
                isLogin: false,
                version: ThirdPartyTool.Version,
                isTeamsChannel: CommonUtil.isTeamsChannel(),
                isCustomCssEnable: Config.CUSTOM_CSS_ENABLE,
                userTypeContent: Text['userName'],
                UserInfo: {
                    userName: '',
                    userIcon: '',
                    userMode: '',
                },
                progress: 0,
                progressId: '',
                pin: true,
                isMobile: parent.EcpController._isMobile(),
                AutoSpeech: false,
                size: 's',
                Fontsize: 's',
            },
            methods: {
                doToggle(event) {
                    Main.doToggle();
                },
                doHistoryToggle(event) {
                    Main.doHistoryToggle();
                },
                openUserInfo(event) {
                    $('#accountView').modal('toggle');
                },
                doLogout(event) {
                    Main.doLogout();
                },
                doChangeSize(size) {
                    Main.doChangeSize(size);
                },
                doTogglePin() {
                    if (!CommonUtil.isTeamsChannel()) {
                        parent.EcpController.pin = !parent.EcpController.pin;
                        Main.PageApp.pin = parent.EcpController.pin;
                    } else {
                        console.warn('[Main] is teams channel , not support toggle pin.');
                    }
                },
                switchAutoSpeech() {
                    Main.switchAutoSpeech();
                },
                switchFontSize(isdefault) {
                    Main.switchFontSize(isdefault);
                },
            },
            watch: {},
        });
    },

    doChangeSize(size) {
        parent.EcpController.NOW_SIZE = size;
        Main.PageApp.size = size;
        if (Main.PageApp.isMobile) {
            console.debug('[Main - doChangeSize] Mobile can not change size.');
            return;
        }
        let mainView = parent.EcpController.KM_COPILOT_ID;
        let webView = parent.EcpController.KM_COPILOT_WEB_ID;
        let history = parent.EcpController.KM_COPILOT_HISTORY_ID;
        switch (size) {
            case 's': //default
                parent.document.getElementById(mainView).style.minWidth = '380px';
                parent.document.getElementById(mainView).style.width = 'calc(43vh - 0px)';
                parent.document.getElementById(webView).style.width = 'calc(70vh - 0px)';
                parent.document.getElementById(history).style.width = 'calc(25vh - 0px)';
                Main._resizeWeb(webView);
                Main._resizehistory(history);
                break;
            case 'm':
                let width = parent.document.body.clientWidth / 2;
                let width_screen = 620;
                parent.document.getElementById(webView).style.right = width > width_screen ? '50.5%' : '625px';
                parent.document.getElementById(webView).style.width = '48%';
                parent.document.getElementById(history).style.right = width > width_screen ? '50.5%' : '625px';
                parent.document.getElementById(mainView).style.width = width > width_screen ? '50%' : '620px';
                Main._resizehistory(history);
                break;
            case 'l':
                const calculate = Main.calculateIframeSize(parent.document.body.clientWidth);
                if (parent.document.getElementById(history).classList.contains('animate__fadeInRight')) {
                    parent.document.getElementById(mainView).style.width = calculate.mainWidth;
                    parent.document.getElementById(webView).style.width = calculate.mainWidth;
                    parent.document.getElementById(history).style.right = calculate.sideRight;
                  } else {
                    parent.document.getElementById(mainView).style.width = '99%';
                    parent.document.getElementById(webView).style.width = '99%';
                  }
                  
                  parent.document.getElementById(webView).style.right = '0px';
                break;
        }
    },
    calculateIframeSize(screenWidth) {
        let mainWidth, sideRight;
        if (screenWidth <= 960) {
            mainWidth = '71%';
            sideRight = '71.3%';
        } else if (screenWidth <= 1279) {
            mainWidth = '74%';
            sideRight = '74.4%';
        }else if (screenWidth <= 1440) {
            mainWidth = '77.5%';
            sideRight = '77.8%';
        } else if (screenWidth <= 1600) {
            mainWidth = '80.6%';
            sideRight = '80.9%';
        } else if (screenWidth <= 1745) {
            mainWidth = '82.7%';
            sideRight = '83%';
        } else if (screenWidth <= 1920) {
            mainWidth = '84%';
            sideRight = '84.3%';
        }else {
            mainWidth = '85%';
            sideRight = '85.3%';
        }
        return {mainWidth,sideRight};
    },
    doToggle() {
        parent.EcpController.doToggle();
    },
    doHistoryToggle() {
        parent.EcpController.doHistoryToggle();
        parent.EcpController.setHistoryPosition();
        if(parent.EcpController.NOW_SIZE == 'l'){
            Main.doChangeSize(parent.EcpController.NOW_SIZE)
        }
    },

    goPage(path) {
        ViewController.setView(path);
    },

    switchAutoSpeech() {
        Main.PageApp.AutoSpeech = !Main.PageApp.AutoSpeech;
        document.getElementById('mainFrame').contentWindow.Chat.isAutoSpeechSynthesis = Main.PageApp.AutoSpeech;
        document.getElementById('mainFrame').contentWindow.Chat.speakingCancel();
        document.getElementById('mainFrame').contentWindow.WebSpeechSynthesis.speak("  ");
    },


    switchFontSize(isdefault) {
        if (isdefault) {

        } else {
            switch (Main.PageApp.Fontsize) {
                case "l":
                    Main.PageApp.Fontsize = "s";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
                case "m":
                    Main.PageApp.Fontsize = "l";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
                case "s":
                    Main.PageApp.Fontsize = "m";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
            }
        }

    },
    showLoading(show, animate) {
        console.debug('[Main-showLoading] show = ' + show + ' ,animate = ' + animate);
        if (show) {
            Main.PageApp.progress = 10;
            $('#loading').show(animate);
            Main.PageApp.progressId = setInterval(function () {
                if (Main.PageApp.progress >= 90) {
                    Main._cleanTimer();
                } else {
                    Main.PageApp.progress += 5;
                }
            }, 1000 * 5);
            Main.TimeerCleanList.push(Main.PageApp.progressId);
        } else {
            Main.PageApp.progress = 100;
            $('#loading').hide();
            Main._cleanTimer();
        }
    },

    doNotReady() {
        Swal.fire({
            icon: 'error',
            title: Text['errorNotReadyTitle'],
            text: Text['errorNotReadyContent'],
            showCloseButton: false,
            showCancelButton: false,
            showConfirmButton: false,
        });
        this.showLoading(false);
    },

    doLogout() {
        try {
            switch (CommonUtil.getLocalStorage('UserInfo').mode) {
                case Main.MODE_USER:
                    let tokenId = CommonUtil.getLocalStorage('UserInfo').tokenData.tokenId;;
                    EcpUserLogin.doLogout(tokenId);
                    break;
                default:
                    console.log('[Main-doLogout] Anonymous logout.');
                    break;
            }
            $('#userIcon').hide(100);
            $('#historyIcon').hide(100);
            $('#mainFontSize').hide(100);
            $('#accountView').modal('toggle');
            CommonUtil.send(parent.EcpController.KM_COPILOT_HISTORY_ID,"cleanHistoryList" ,{});
            Main.PageApp.UserInfo.userName = '';
            Main.PageApp.UserInfo.userIcon = '';
            Main.PageApp.UserInfo.userMode = '';
            Main.goPage(ViewController.PAGE.LOGIN);
            Main.PageApp.showLogout = false;
            Main.PageApp.showLeave = false;
            Main.PageApp.AutoSpeech = false;
            Main.PageApp.isLogin = false;
            if (parent.EcpController.SHOW_WEB_TOGGLE) {
                parent.EcpController.doWebViewToggle();
            }
            if (parent.EcpController.SHOW_HISTORYTOGGLE) {
                parent.EcpController.doHistoryToggle();
            }

            if (document.getElementById('mainFrame').contentWindow.Chat) {
                document.getElementById('mainFrame').contentWindow.Chat.cleanMessage();
            }
            CommonUtil.deleteLocalStorage('UserInfo');
            let soure = CommonUtil.getLocalStorage('source');
            if ('teams' == soure) {
                Main.doChangeSize('l');
            } else {
                Main.doChangeSize(Config.COPILOT_SIZE);
                Main.switchFontSize(true);
            }
            setTimeout(function () {
                Main.showLoading(false);
            }, 1500);
        } catch (e) {
            console.warn('[Main - doLogout] not ready.');
            location.reload();
        }
    },

    _resizeWeb(webView) {
        if (parent.EcpController.SHOW_WEB_TOGGLE) {
            parent.$('#' + webView).hide();
        }
        setTimeout(function () {
            parent.EcpController.setWebViewPosition();
            if (parent.EcpController.SHOW_WEB_TOGGLE) {
                parent.$('#' + webView).show(250);
            }
        }, 1000);
    },

    _resizehistory(history) {
        if (parent.EcpController.SHOW_HISTORYTOGGLE) {
            parent.$('#' + history).hide();
        }
        setTimeout(function () {
            parent.EcpController.setHistoryPosition();
            if (parent.EcpController.SHOW_HISTORYTOGGLE) {
                parent.$('#' + history).show(250);
            }
        }, 1000);
    },

    _cleanTimer() {
        Main.TimeerCleanList.forEach(function (timer) {
            try {
                clearInterval(timer);
            } catch (e) {
                console.warn(e);
            }
        });
        Main.TimeerCleanList = [];
    }
}