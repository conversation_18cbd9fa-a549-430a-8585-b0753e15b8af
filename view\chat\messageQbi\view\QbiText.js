var QbiText = {
    create(answer) {
        
        answer.text = (Array.isArray(answer.text) ? answer.text[0] : answer.text).replaceAll('\n\n', '\n');
        answer.text = (Array.isArray(answer.text) ? answer.text[0] : answer.text).replaceAll('\n', '<br>');
        answer.text = (Array.isArray(answer.text) ? answer.text[0] : answer.text).replaceAll('\\n', '<br>');
        return answer.text;
    }
}