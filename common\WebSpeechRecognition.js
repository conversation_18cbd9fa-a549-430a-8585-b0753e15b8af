/**
 * @desc 語音輸入功能
 *
 * 語系參數 cmn-Hant-TW
 * 語系參數(Chat.PageApp.QbiCopilotInfo.sttLang)
 *  - 繁體中文: 'cmn-Hant-TW',
 *  - 普通話: 'cmn-Hans-CN',
 *  - 英文: 'en-US',
 *  - 泰文: 'th-TH',
 *  - 越南文: 'vi-VN',
 *  - 印尼語: 'id-ID',
 */
var WebSpeechRecognition = {
    recognition: null,
    /**
     * @desc 是否正常啟用
     */
    isSupport: false,
    /**
     * @desc 來源裝置:windows、ipad、iphone、android
     */
    device: "",
    /**
     * @desc 語音轉文字後辨識完畢的文字
     */
    finalTranscript: "",
    /**
     * @desc 狀態：init、recognizing、end
     */
    status: "init",
    /**
     * @desc 觸發取消原因：click 、 stopEvent
     */
    cancelReason: "",
    /**
     * @desc 啟用時間
     */
    startTimestamp: null,
    /**
     * @desc 初始化
     */
    init: function () {
        WebSpeechRecognition.finalTranscript = "";
        WebSpeechRecognition.status = "init";
        WebSpeechRecognition.setSpeechBtn();
    },
    /**
     * @desc 設定按鈕
     */
    setSpeechBtn: function () {
        Chat.setSpeechDisPlay(WebSpeechRecognition.status);
    },

    /**
     * @desc 取得裝置:windows、ipad、iphone、android
     */
    getDevice: function () {
        const userAgent = navigator.userAgent;
        if (/Android/i.test(userAgent)) WebSpeechRecognition.device = "android";
        if (/iPhone/i.test(userAgent)) WebSpeechRecognition.device = "iphone";
        if (/iPad/i.test(userAgent)) WebSpeechRecognition.device = "ipad";
        if (/Windows/i.test(userAgent)) WebSpeechRecognition.device = "windows";
    },
    /**
     * @desc  第一次載入完畢後處理
     */
    doLoad: function (event) {
        WebSpeechRecognition.getDevice();
        if (!("webkitSpeechRecognition" in window)) {
            CommonUtil.swalWarnMessage('noSpeechError');
            WebSpeechRecognition.isSupport = false;
            CommonUtil.swalWarnMessage('browserSupportError');
        }
        else {
            WebSpeechRecognition.isSupport = true;

            /**開始語音辨識*/
            if (event.target.id === "SpeechToTextBtn") {
                WebSpeechRecognition.startRecognition(event);
                /**結束語音辨識*/
            } else if (event.target.id === "SpeechToTextEndBtn") {
                WebSpeechRecognition.cancelRecognition("click");
            }
            WebSpeechRecognition.setSpeechBtn();

            /** Start 監聽 */
            let interval;

            WebSpeechRecognition.recognition.onstart = function () {
            };

            WebSpeechRecognition.recognition.onsoundstart = function () {
                interval = setInterval(function () {
                    Chat.disPlayMode("SiriWaveDuring_Start");
                }, 100);
            };

            WebSpeechRecognition.recognition.onsoundend = function () {
                clearInterval(interval);
                Chat.disPlayMode("SiriWaveDuring_End");
            };

            WebSpeechRecognition.recognition.onnomatch = function () {
                /** 安卓 Chrome  開始偵測約3秒內未偵測到返回此錯誤*/
                CommonUtil.swalWarnMessage('noSpeechError');
            };

            /** Error 監聽 */
            WebSpeechRecognition.recognition.onerror = function (event) {
                /** PC Chrome 開始偵測約8秒內未偵測到返回此錯誤 */
                if (event.error == "no-speech") {
                    CommonUtil.swalWarnMessage('noSpeechError');
                }
                if (event.error == "audio-capture") {
                    CommonUtil.swalWarnMessage('audioCaptureError');
                }
                if (event.error == "not-allowed") {
                    CommonUtil.swalWarnMessage('microAccessError');
                }
                if (event.error == "aborted") {
                    CommonUtil.swalWarnMessage('microAccessError');
                }

                WebSpeechRecognition.init();
            };

            /** End 監聽 */
            WebSpeechRecognition.recognition.onend = function () {
                /** PC Chrome 若發話偵測後約 14 秒內未偵測到發話會自動結束 */
                /** Android Chrome 若發話偵測後約 2 秒內未偵測到發話會自動結束 */
                // Chat.doSendButtonClick();
                WebSpeechRecognition.status = "end";
                WebSpeechRecognition.setSpeechBtn();
            };

            /** 識別結果監聽 */
            WebSpeechRecognition.recognition.onresult = function (event) {
                var interim_transcript = "";
                for (var i = event.resultIndex; i < event.results.length; ++i) {
                    if (event.results[i].isFinal) {
                        if (WebSpeechRecognition.device === "android" || WebSpeechRecognition.device === "ipad")
                            WebSpeechRecognition.finalTranscript = event.results[i][0].transcript;
                        else WebSpeechRecognition.finalTranscript += event.results[i][0].transcript;
                    }
                }
                WebSpeechRecognition.finalTranscript = WebSpeechRecognition.capitalize(
                    WebSpeechRecognition.finalTranscript
                );
                let sttWord = WebSpeechRecognition.finalTranscript;
                if (WebSpeechRecognition.device !== "android" && WebSpeechRecognition.device !== "ipad")
                    sttWord = WebSpeechRecognition.finalTranscript + interim_transcript;
                Chat.sttSendMessage(sttWord);
            };
        }

    },
    /**
     * @desc 取消語音輸入
     * @param {Element} reason 觸發取消原因:click 、 stopEvent
     */
    cancelRecognition: function (reason) {
        WebSpeechRecognition.cancelReason = reason;
        if (WebSpeechRecognition.status === "recognizing") WebSpeechRecognition.recognition.stop();
    },
    /**
     * @desc 開始語音輸入
     * @param {event} event
     */
    startRecognition: function (event) {
        Chat.speakingCancel();
        WebSpeechRecognition.recognition = new webkitSpeechRecognition();
        /** 如果設定為 true，表示除非我們停止辨識，不然就會一直持續的辨識語音轉換為文字，如果設定為 false，在辨識一段話完成之後就會結束辨識。*/
        WebSpeechRecognition.recognition.continuous = false;
        /** 如果設定為 true，表示在我們講話的當下就會即時辨識，不然就會在一段話結束之後，才會開始辨識。 */
        WebSpeechRecognition.recognition.interimResults = true;
        WebSpeechRecognition.finalTranscript = "";
        WebSpeechRecognition.recognition.lang = Chat.PageApp.QbiCopilotInfo.sttLang;
        WebSpeechRecognition.status = "recognizing"
        WebSpeechRecognition.startTimestamp = event.timeStamp
        WebSpeechRecognition.recognition.start();
    },
    /**
     * @desc 文字轉換
     */
    capitalize: function (s) {
        return s.replace(/\S/, function (m) {
            return m.toUpperCase();
        });
    },
};
