var QbiImage = {

    TEMPLATE: `
        <a target="_blank" href="{href}">
            <img width="100%" src="{src}">
        </a>
    `,
    create(answer) {
        let html = QbiImage.TEMPLATE;
        let imageClickEnable = !CommonUtil.isStringEmpty(answer.imageClickUrl);
        html = html.replaceAll('{href}', imageClickEnable ? answer.imageClickUrl : answer.imageUrl);
        html = html.replaceAll('{src}', (answer.thumbnailUrl == null || answer.thumbnailUrl == "") ? answer.imageUrl : answer.thumbnailUrl);
        return html;
    }
}