var MessageController = {

    TARGET: '#messageList',
    COUNT: 1,

    RIGHT: 'ChatMessageRight animate__animated animate__fadeInRight',
    LEFT: 'ChatMessageLeft animate__animated animate__fadeInLeft',

    StorageTOPN_KEY: 'QbiCopilotStorageTOPN_KEY',
    TotalMessage_Synthesis: "",

    NoticeInterval: null,
    goChatBottomTimeout: null,
    parser: null,
    doInit() {
        CommonUtil.setLocalStorage(MessageController.StorageTOPN_KEY, JSON.stringify({}));
        ThirdPartyTool.includeJS('./message/MessageText.js');
        ThirdPartyTool.includeJS('./message/MessageNotice.js');
        ThirdPartyTool.includeJS('./message/MessageGptText.js');
        ThirdPartyTool.includeJS('./message/MessageWebSearchText.js');
        ActionManager.doInit();
        QbiAnswer.doInit();
        setTimeout(function () {
            MessageController.doInitCreator();
            MessageController._initConfig(ActionFunction.initFunctionList);
            if (ChatEvent.Mode == "PLUGIN") {
                MessageController._initShowMore();
                MessageController._initTTS();
            }
            ChatCataLog.init();
            MessageController.cleanChatMemory();
            console.log('[MessageController doInitCreator] finish.');
        }, 1000);
        setTimeout(function () {
            parent.Main.showLoading(false, 0);
            console.log('[MessageController] check.');
        }, 1000 * 3);

    },

    doInitCreator() {
        MessageController['text'] = MessageText.create;
        MessageController['notice'] = MessageNotice.create;
        MessageController['gptText'] = MessageGptText.create;
        MessageController['websearchtext'] = MessageWebSearchText.create;
    },

    doShowGreeting() {
        let greeting = Chat.PageApp.ServerConfig.greeting;
        if (greeting != undefined) {
            let mode = CommonUtil.getLocalStorage('UserInfo').mode;
            if (parent.Main.MODE_USER === mode) {
                if (!CommonUtil.isStringEmpty(greeting.FInsideKMId)) {
                    ActionCopilot.doDisaplyAnswer(null, greeting.FInsideKMId)
                }
            } else {
                if (!CommonUtil.isStringEmpty(greeting.FOutterKMId)) {
                    ActionCopilot.doDisaplyAnswer(null, greeting.FOutterKMId);
                }
            }
        }
    },

    doAddMessage(content, source) {
        let html = `<div id='Message_{count}' class='ChatMessage {source}'>{content}</div>`;
        html = html.replace('{count}', MessageController.COUNT);
        html = html.replace('{source}', source);
        html = html.replace('{content}', content);

        if(MessageController.COUNT === 1){
            html = MessageController.replaceConstWithVarInScript(html);
        }
        $(MessageController.TARGET).append(html);
        MessageController.COUNT++;
        requestAnimationFrame(() => {MessageController._goChatBottom();});
        MessageController.cleanNoticeInterval();
        MessageController.setNoticeInterval(html);
        if (html.indexOf("satisfactionBtnDislike") !== -1 && Config.CUSTOM_CSS_ENABLE) {
            let dislike = $('.satisfactionBtnDislike').last();
            let ok = $('.satisfactionBtnOk').last();
            let like = $('.satisfactionBtnLike').last();
            let copy = $('.satisfactionBtnCopy').last();
            dislike[0].src = '../../image/custom-style-01/dislike_custom.png';
            ok[0].src = '../../image/custom-style-01/ok_custom.png';
            like[0].src = '../../image/custom-style-01/like_custom.png';
            copy[0].src = '../../image/custom-style-01/copy_custom.png';
        }    
        if(source.includes(MessageController.LEFT) && !content.includes("MessageNotice") && document.getElementById('messageList').innerHTML.includes("Message_2")) {
            // 如果是通知訊息，則不儲存到聊天紀錄
            MessageController.doAddChatHistory().then(() => {
                ChatEvent.saveChatMessage();
            })
            return;
        }
    },
    replaceConstWithVarInScript(html) {
        // 這個正則會找出所有 <script> ... </script> 區塊
        return html.replace(/<script\b[^>]*>([\s\S]*?)<\/script>/gi, function(match, scriptContent) {
            // 只把 script 內容裡的 const 換成 var
            const newScriptContent = scriptContent.replace(/\bconst\b/g, 'var');
            return `<script>${newScriptContent}</script>`;
        });
    },

    setNoticeInterval(html) {
        if (html.indexOf(Chat.PageApp.NoticeContent.noticeId) && Chat.PageApp.NoticeContent.noticeId != null) {
            let loadingText = document.getElementById(Chat.PageApp.NoticeContent.noticeId);
            loadingText.style.fontWeight = "bold";
            let dotCount = 1;
            // 每 200 毫秒更新一次文本内容
            MessageController.NoticeInterval = setInterval(function () {
                MessageController.updateLoadingText(dotCount, loadingText);
                dotCount++;
            }, 300);
        }
    },
    updateLoadingText(dotCount, loadingText) {
        dotCount = (dotCount + 1) % 7; // 循环 dotCount 在 0 到 4 之间
        let dots = '.'.repeat(dotCount); // 根据 dotCount 生成点字符串
        loadingText.textContent = Chat.PageApp.NoticeContent.content + dots; // 更新文本内容
    },

    cleanNoticeInterval() {
        if (MessageController.NoticeInterval != null) {
            let loadingText = document.getElementById(Chat.PageApp.NoticeContent.noticeId);
            loadingText.parentElement.style.display = "none";
            clearInterval(MessageController.NoticeInterval);
            MessageController.NoticeInterval = null;
            Chat.PageApp.NoticeContent.noticeId = null;
            Chat.PageApp.NoticeContent.content = null;
        }
    },


    cleanMessage(callback) {
        if (MessageController.COUNT != 1) {
            MessageController.COUNT = 1;
            $(MessageController.TARGET).html('');
            MessageController.cleanChatMemory(callback);
        }
    },

    cleanChatMemory(callback) {
        try {
            let args = {
                chatId: UserInfo.getData().userId
            }
            CommonUtil.deleteLocalStorage(ActionCopilot.KEY_LAST_ARGS);
            HttpQuery.doRequest('/openapi/copilot/clearChatHistory', args, function (result) {
                if (!result.success) {
                    Swal.fire({
                        icon: 'error',
                        title: Text['errorTitle'],
                        text: Text['errorContent'],
                        confirmButtonColor: '#8ba8d9',
                    });
                }
                if (callback != undefined) {
                    callback();
                }
            });
        } catch (e) {
            console.warn('[MessageController-cleanChatMemory] not ready.');
        }
    },

    cleanChatMemory(callback) {
        try {
            return new Promise((resolve, reject) => {
                let args = {
                    chatId: UserInfo.getData().userId
                }
                CommonUtil.deleteLocalStorage(ActionCopilot.KEY_LAST_ARGS);
                HttpQuery.doRequest('/openapi/copilot/clearChatHistory', args, function (result) {
                    if (!result.success) {
                        Swal.fire({
                            icon: 'error',
                            title: Text['errorTitle'],
                            text: Text['errorContent'],
                            confirmButtonColor: '#8ba8d9',
                        });
                    } else {
                        resolve();
                    }
                    if (callback != undefined) {
                        callback();
                    }
                });
            })
        } catch (e) {
            console.warn('[MessageController-cleanChatMemory] not ready.');
        }
    },
    doAddChatHistory() {
        return new Promise((resolve, reject) => {
            try {
                if ((ActionFunction.TASK_Switch || ActionManager.isDoAddHistory)) {
                    ActionFunction.HISTORY_RECORD.messages[1] = ActionFunction.HISTORY_RECORD_AI
                    ActionFunction.HISTORY_RECORD.chatId = UserInfo.getData().userId;
                    ActionFunction.TASK_Switch = false;
                    ActionManager.isDoAddHistory = false;
                    HttpQuery.doRequest('/openapi/copilot/addChatHistory', ActionFunction.HISTORY_RECORD, function (result) {
                        resolve();
                    })
                }else{
                    resolve();
                }
            } catch (e) {
                console.warn('[MessageController-doAddChatHistory] not ready.');
            }
        })
    },
    doHandleHistoryToAddChatHistory(data) {
        try {
            let historyList = { messages: [] , chatId: UserInfo.getData().userId};
            data.forEach((item) => {
                item = JSON.parse(item);
                if (item.type === "human") {
                    historyList.messages.push({
                        type: "human",
                        message: item.data.content
                    });
                } else if (item.type === "ai") {
                    historyList.messages.push({
                        type: "ai",
                        message: item.data.content
                    });
                }
            });
            HttpQuery.doRequest('/openapi/copilot/addChatHistory', historyList, function (result) {
            })
        } catch (e) {
            console.warn('[MessageController-doHandleHistoryToAddChatHistory] not ready.');
        }
      },
    //顯示知識內容
    doParseKMMesaage(response, topN, args) {
        try {
            let answerMode = Chat.PageApp.ServerConfig.answerMode;
            let answerType = response.answerType;
            if ("gptText" == answerType) {
                let chunkMap = {};

                // topN 來源
                for (let i = 0; i < topN.length; i++) {
                    let topN_item = topN[i];
                    let chunkId = topN_item.metadata.chunkId;
                    if (!chunkMap.hasOwnProperty(chunkId)) {
                        chunkMap[chunkId] = topN_item;
                    }
                }
                args.chunkMap = chunkMap;

                // 顯示清單
                let answerSource = {};
                if (ActionCopilot.ANSWER_GPT_PLUS_EASY == answerMode) {
                    let answers = response.answer["chunkId"].split(",");
                    for (let i = 0; i < answers.length; i++) {
                        try {
                            let sourceId = chunkMap[answers[i]].metadata.sourceId;
                            if (!answerSource.hasOwnProperty(sourceId)) {
                                answerSource[sourceId] = answers[i];
                            }
                        } catch (e) {
                            console.error("[MessageController-doParseKMMesaage] error:" + e);
                        }
                    }
                } else {
                    let answers = response.answer.answers;
                    for (let i = 0; i < answers.length; i++) {
                        try {
                            let kmId = chunkMap[answers[i].chunkId].metadata.knowledgeId;
                            if (!answerSource.hasOwnProperty(kmId)) {
                                answerSource[kmId] = chunkMap[answers[i].chunkId];
                            }
                        } catch (e) {
                            console.error("[MessageController-doParseKMMesaage] error:" + e);
                        }
                    }
                }
                args.answerSource = answerSource;

                // 呈現內容
                let showAnswer = Object.keys(answerSource);

                if (showAnswer.length == 1) {
                    // 如果只有一筆，顯示唯一的答案
                    let kmId = showAnswer[0];
                    let chunk = answerSource[kmId];
                    let messageId = response.messageId
                    Chat.Chat_kmIds = [kmId];
                    Chat.Chat_messageId = messageId;
                    Chat.Chat_dataSource = "km";
                    ActionCopilot.doDisaplyAnswer(messageId, kmId, chunk);
                    // 防止Loading異常
                    if (showAnswer.length > 0) {
                        setTimeout(function () {
                            parent.Main.showLoading(false, 250);
                        }, 200 * showAnswer.length);
                    }
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                } else {
                    // 多筆知識，重新排序
                    ActionCopilot.doShowCards(args, response.messageId);
                }

            } else if ("text" == answerType && ActionCopilot.ANSWER_GPT !== answerMode) {
                // 多筆知識，重新排序
                if (args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠")) {
                    ActionManager.isDoAddHistory = true;
                    Chat.Chat_answer = response.answer;
                    Chat.Chat_answerType = answerType;
                    Chat.Chat_dataSource = "llm";
                    let content = MessageController.text(response.answer);
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                }
                ActionCopilot.doShowCards(args, null);
                return;
            }
        } catch {
            if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                MessageController.doParseGoogleSearchMessage(args, null);
            } else {
                ChatEvent._showError();
                console.error('[MessageController-doParseGPTMessage] error:' + e);
            }
        }
    },

    //GPT答案顯示模式
    doParseGPTMessage(response, topN, inputQuestion,args) {
        try {
            let answerType = response.answerType;
            // 一般文字回答
            if (("text" == answerType || response.answer['answers'].length == 0)) {
                if (inputQuestion.startsWith("@") || inputQuestion.startsWith("＠")) {
                    let showContent = {
                        needSatisfaction: false,
                        needSpeechSynthesis: true,
                        content: response.answer,
                        messageId: response.messageId
                    }
                    ActionManager.isDoAddHistory = true;
                    Chat.Chat_answer = JSON.stringify({ text: showContent.content, type: "text" });
                    Chat.Chat_answerType = "gptText";
                    Chat.Chat_dataSource = "llm";
                    let content = MessageController.text(showContent);
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                }
                if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                    MessageController.doParseGoogleSearchMessage(args, null);
                    return;
                } else {
                    let showContent = {};
                    if (Chat.PageApp.ServerConfig.answerMode === ActionCopilot.ANSWER_GPT) {
                        showContent = {
                            needSatisfaction: true,
                            needSpeechSynthesis: true,
                            content: response.answer,
                            messageId: response.messageId
                        }
                        ActionManager.isDoAddHistory = true;
                    } else {
                        showContent = {
                            needSatisfaction: true,
                            needSpeechSynthesis: true,
                            content: Chat.PageApp.ServerConfig?.noAnswerReply ? Chat.PageApp.ServerConfig?.noAnswerReply:Text["chatNotFound"],
                            messageId: response.messageId
                        }
                    }
                    Chat.Chat_answer = JSON.stringify({ text: showContent.content, type: "text" });
                    Chat.Chat_answerType = "text";
                    if (Chat.PageApp.ServerConfig.answerMode === ActionCopilot.ANSWER_GPT) {
                        Chat.Chat_dataSource = "text";
                    } else {
                        Chat.Chat_dataSource = "noAnswer";
                    }
                    let content = MessageController.text(showContent);
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                }
            }
            // 支援Qbi答案格式
            if ("qbi" == answerType) {
                alert('Qbi answer not support. please check.');
                Chat.PageApp.LOCK_SEND = false;
                return;
            }

            // GPT 專用回應格式
            let answers = response.answer['answers'];
            let relates = response.answer['follow-up_question_answer'];
            if (relates == undefined) {
                relates = [];
            }

            let sources = [];
            let finalAnswer = "";
            let sourcesMap = {};
            let chunkMap = {};

            // topN 來源
            for (let i = 0; i < topN.length; i++) {
                let topN_item = topN[i];
                let chunkId = topN_item.metadata.chunkId;
                if (!chunkMap.hasOwnProperty(chunkId)) {
                    chunkMap[chunkId] = topN_item;
                }
            }

            // 裝填來源屬性
            for (let i = 0; i < answers.length; i++) {
                let answer = answers[i];
                let answerChunkId = answer.chunkId;
                let answerResult = Object.assign({}, chunkMap[answerChunkId]);
                answerResult.count = Object.keys(sourcesMap).length + 1;
                if (!sourcesMap.hasOwnProperty(answerResult.metadata.sourceId)) {
                    sourcesMap[answerResult.metadata.sourceId] = answerResult;
                }
            }


            // 答案清整
            let answerMap = {};
            let answerCheckMap = {};
            for (let i = 0; i < answers.length; i++) {
                let list = [];
                let answerCheckList = [];
                let sourceId = chunkMap[answers[i].chunkId].metadata.sourceId;
                if (answerMap.hasOwnProperty(answers[i].answer)) {
                    list = answerMap[answers[i].answer];
                }
                if (answerCheckMap.hasOwnProperty(answers[i].answer)) {
                    answerCheckList = answerCheckMap[answers[i].answer];
                }
                // 判斷是否已存在知識
                if (!answerCheckList.includes(sourceId)) {
                    list.push(answers[i].chunkId);
                    answerCheckList.push(sourceId);
                }
                answerMap[answers[i].answer] = list;
                answerCheckMap[answers[i].answer] = answerCheckList;
            }

            // 資料整理  
            for (let i = 0; i < Object.keys(answerMap).length; i++) {
                let answer = Object.keys(answerMap)[i];
                let chunkIds = answerMap[answer];
                answer = answer.replaceAll('\n\n', '\n');
                answer = answer.replaceAll('\n', '<br/>');
                finalAnswer += answer;
                for (let j = 0; j < chunkIds.length; j++) {

                    let sourceId = chunkMap[chunkIds[j]].metadata.sourceId;
                    let source = `<a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{content}','{type}','{mark}')" title="{title}">[{count}]</a>`;
                    source = source.replaceAll('{content}', sourcesMap[sourceId].metadata.sourceName);
                    if (chunkMap[chunkIds[j]].metadata.type == "km") {
                        source = source.replaceAll('{id}', sourcesMap[sourceId].metadata.knowledgeId);
                        source = source.replaceAll('{mark}', escape(sourcesMap[sourceId].page_content));
                        source = source.replaceAll('{type}', 'km'); //開啟知識預覽
                    } else if (chunkMap[chunkIds[j]].metadata.type == "file") {
                        source = source.replaceAll('{id}', sourcesMap[sourceId].metadata.sourceId);
                        source = source.replaceAll('{type}', 'file'); //開啟知識預覽
                    } else {
                        source = source.replaceAll('{id}', sourcesMap[sourceId].metadata.sourceId);
                        source = source.replaceAll('{type}', 'document'); //開啟知識預覽
                    }
                    sourcesMap[sourceId]?.metadata?.knowledgeId && Chat.Chat_kmIds.push(sourcesMap[sourceId].metadata.knowledgeId);
                    source = source.replaceAll('{title}', '[' + sourcesMap[sourceId].metadata.sourceName + ']\n' + CommonUtil.stripHTML(sourcesMap[sourceId].page_content, false));
                    source = source.replaceAll('{count}', sourcesMap[sourceId].count);
                    finalAnswer += source;
                }

                if (i != Object.keys(answerMap).length - 1) {
                    finalAnswer += "，";
                } else {
                    finalAnswer += "。";
                }
            }

            // 來源
            for (const [key, value] of Object.entries(sourcesMap)) {
                let item = {
                    id: key,
                    knowledgeId: value.metadata.knowledgeId,
                    count: value.count,
                    content: value.metadata.sourceName,
                    page_content: value.page_content,
                    type: value.metadata.type,
                };
                sources.push(item);
            }
            // 顯示畫面
            let showContent = {
                needSpeechSynthesis: true,
                content: finalAnswer,
                sources: sources,
                relateds: relates,
                inputQuestion: inputQuestion,
                messageId: response.messageId
            }
            ActionManager.isDoAddHistory = true;
            Chat.Chat_answer = response;
            Chat.Chat_messageId = response.messageId;
            Chat.Chat_answerType = answerType;
            MessageController.doAddMessage(MessageController.gptText(showContent), MessageController.LEFT);
            Chat.PageApp.LOCK_SEND = false;
        } catch (e) {
            if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                MessageController.doParseGoogleSearchMessage(args, null);
            } else {
                ChatEvent._showError();
                console.error('[MessageController-doParseGPTMessage] error:' + e);
            }
        }
    },
    //GPT(簡易版)答案顯示模式
    doParseGPT_EasyMessage(response, topN, inputQuestion) {
        try {
            let answerType = response.answerType;

            // 一般文字回答
            if ("text" == answerType || response.answer['answers'].length == 0) {
                if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                    MessageController.doParseGoogleSearchMessage(args, null);
                } else {
                    let showContent = {
                        needSatisfaction: true,
                        needSpeechSynthesis: true,
                        content: Chat.PageApp.ServerConfig?.noAnswerReply ? Chat.PageApp.ServerConfig?.noAnswerReply:Text["chatNotFound"],
                        messageId: response.messageId
                    }
                    let content = MessageController.text(showContent);
                    Chat.Chat_answer = JSON.stringify({ text: showContent.content, type: "text" });
                    Chat.Chat_answerType = "text";
                    Chat.Chat_dataSource = "noAnswer";
                    MessageController.doAddMessage(content, MessageController.LEFT);
                }
                Chat.PageApp.LOCK_SEND = false;
                return;
            }

            // 支援Qbi答案格式
            if ("qbi" == answerType) {
                alert('Qbi answer not support. please check.');
                Chat.PageApp.LOCK_SEND = false;
                return;
            }

            // GPT 專用回應格式
            let answer = [];
            answer.push(response.answer);
            let relates = response.answer['follow-up_question_answer'];
            if (relates == undefined) {
                relates = [];
            }

            let sources = [];
            let finalAnswer = "";
            let sourcesMap = {};
            let chunkMap = {};

            // topN 來源
            for (let i = 0; i < topN.length; i++) {
                let topN_item = topN[i];
                let chunkId = topN_item.metadata.chunkId;
                if (!chunkMap.hasOwnProperty(chunkId)) {
                    chunkMap[chunkId] = topN_item;
                }
            }

            // 裝填來源屬性
            try {
                for (let i = 0; i < answer.length; i++) {
                    let answerChunkId = answer[i]["chunkId"];
                    let answerResult = Object.assign({}, chunkMap[answerChunkId]);
                    answerResult.count = Object.keys(sourcesMap).length + 1;
                    if (!sourcesMap.hasOwnProperty(answerResult.metadata.sourceId)) {
                        sourcesMap[answerResult.metadata.sourceId] = answerResult;
                    }
                }
            } catch {
                answer = [];
                for (let i = 0; i < topN.length; i++) {
                    let answerResult = Object.assign({}, topN[i]);
                    answerResult.count = Object.keys(sourcesMap).length + 1;
                    if (!sourcesMap.hasOwnProperty(answerResult.metadata.sourceId)) {
                        let answerItem = { chunkId: "", answer: "" }
                        answerItem["chunkId"] = answerResult.metadata.chunkId;
                        answerItem["answer"] = response.answer["answer"];
                        answer.push(answerItem);
                        sourcesMap[answerResult.metadata.sourceId] = answerResult;
                    }
                }
            }
            // 答案清整
            let answerMap = {};
            let answerCheckMap = {};
            for (let i = 0; i < answer.length; i++) {
                let list = [];
                let answerCheckList = [];
                let sourceId = chunkMap[answer[i]['chunkId']].metadata.sourceId;
                if (answerMap.hasOwnProperty(answer[i]["answer"])) {
                    list = answerMap[answer[i]["answer"]];
                }
                if (answerCheckMap.hasOwnProperty(answer[i]["answer"])) {
                    answerCheckList = answerCheckMap[answer[i]["answer"]];
                }
                // 判斷是否已存在知識
                if (!answerCheckList.includes(sourceId)) {
                    list.push(answer[i]['chunkId']);
                    answerCheckList.push(sourceId);
                }
                answerMap[answer[i]["answer"]] = list;
                answerCheckMap[answer[i]["answer"]] = answerCheckList;
            }

            // 資料整理  
            for (let i = 0; i < Object.keys(answerMap).length; i++) {
                let answer = Object.keys(answerMap)[i];
                let chunkIds = answerMap[answer];
                answer = answer.replaceAll('\n\n', '\n');
                answer = answer.replaceAll('\n', '<br/>');
                finalAnswer += answer;
                for (let j = 0; j < chunkIds.length; j++) {

                    let sourceId = chunkMap[chunkIds[j]].metadata.sourceId;
                    let source = `<a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{content}','{type}','{mark}')" title="{title}">[{count}]</a>`;
                    source = source.replaceAll('{id}', sourcesMap[sourceId].metadata.knowledgeId);
                    source = source.replaceAll('{content}', sourcesMap[sourceId].metadata.sourceName);
                    if (chunkMap[chunkIds[j]].metadata.type == "km") {
                        source = source.replaceAll('{mark}', escape(sourcesMap[sourceId].page_content));
                        source = source.replaceAll('{type}', 'km'); //開啟知識預覽
                    } else if (chunkMap[chunkIds[j]].metadata.type == "file") {
                        source = source.replaceAll('{type}', 'file'); //開啟知識預覽
                    } else {
                        source = source.replaceAll('{type}', 'document'); //開啟知識預覽
                    }
                    source = source.replaceAll('{title}', '[' + sourcesMap[sourceId].metadata.sourceName + ']\n' + CommonUtil.stripHTML(sourcesMap[sourceId].page_content, false));
                    source = source.replaceAll('{count}', sourcesMap[sourceId].count);
                    finalAnswer += source;
                }

                if (i != Object.keys(answerMap).length - 1) {
                    finalAnswer += "，";
                } else {
                    finalAnswer += "。";
                }
            }

            // 來源
            for (const [key, value] of Object.entries(sourcesMap)) {
                let item = {
                    id: key,
                    knowledgeId: value.metadata.knowledgeId,
                    count: value.count,
                    content: value.metadata.sourceName,
                    page_content: value.page_content,
                    type: value.metadata.type,
                };
                sources.push(item);
            }
            // 顯示畫面
            let showContent = {
                needSpeechSynthesis: true,
                content: finalAnswer,
                sources: sources,
                relateds: relates,
                inputQuestion: inputQuestion,
                messageId: response.messageId
            }
            ActionManager.isDoAddHistory = true;
            MessageController.doAddMessage(MessageController.gptText(showContent), MessageController.LEFT);
            Chat.PageApp.LOCK_SEND = false;
        } catch (e) {
            if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                MessageController.doParseGoogleSearchMessage(args, null);
            } else {
                ChatEvent._showError();
                console.error('[MessageController-doParseGPTMessage] error:' + e);
            }
        }
    },

    //GoogleSearch答案顯示模式
    doParseGoogleSearchMessage(args, messageId) {
        //對話紀錄資料清整
        Chat.Chat_messageId = "";
        Chat.Chat_kmIds = [];
        MessageController.TotalMessage_Synthesis = "";
        HttpQuery.doRequest('/openapi/copilot/webSearch', {
            inputQuestion: args.inputQuestion,
            login: args.login,
            chatId: args.chatId,
            summary: args.summary,
        }, function (result) {
            try {
                MessageController.ParseGoogleSearchMessage(result, messageId);
            } catch (e) {
                console.error('[MessageController-webSearch] e:' + e);
                ChatEvent._showError();
            } finally {
                if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                    WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
                }
                Chat.PageApp.LOCK_SEND = false;
            }
        });
    },
    ParseGoogleSearchMessage(result, messageId) {
        let answerType = result.answerType;
        ActionFunction.HISTORY_RECORD_AI.message = 
        result?.answerType === "text" 
        ? result?.answer 
        : result?.answer?.answers.map(item => item.answer).join('');
        Chat.Chat_answer = result;
        Chat.Chat_dataSource = "web";
        // 一般文字回答
        if ("text" == answerType) {
            ActionManager.isDoAddHistory = true;
            MessageController.doAddMessage(MessageController.websearchtext(result.answer, messageId), MessageController.LEFT);
            Chat.PageApp.LOCK_SEND = false;
            return;
        }
        // 支援Qbi答案格式
        if ("qbi" == answerType) {
            alert('Qbi answer not support. please check.');
            Chat.PageApp.LOCK_SEND = false;
            return;
        }

        // GoogleSearch 專用回應格式
        let relates = result.answer['follow-up_question_answer'];
        if (relates == undefined) {
            relates = [];
        }


        let finalAnswer = "";
        let sources = [];
        // 資料整理  
        for (let i = 0; i < result.answer.answers.length; i++) {
            if (result.answer.answers[i].hasOwnProperty("chunkId")) {
                let urlMappingId = result.answer.answers[i]["chunkId"].replace(/\D/g, '');
                let answerItem = result.answer['urlMapping'][urlMappingId];
                if (answerItem != null) {
                    // 資料來源整理  
                    let sourcesMap = `
                    <a href="{src}" target="_blank" title="{title}" >{id} {content}</a>
                    <br/>`;
                    sourcesMap = sourcesMap.replaceAll('{id}', "[" + (i + 1) + "]");
                    sourcesMap = sourcesMap.replaceAll('{content}', answerItem["title"]);
                    sourcesMap = sourcesMap.replaceAll('{title}', answerItem["title"]);
                    sourcesMap = sourcesMap.replaceAll('{src}', answerItem["url"]);
                    sources.push(sourcesMap);

                    // 文字合併、超連結處理  
                    finalAnswer += result.answer.answers[i]["answer"] + "{answerClick}";
                    let answerClick = `
                    <a href="{src}" target="_blank" >{content}</a>
                    `;
                    answerClick = answerClick.replaceAll('{content}', "[" + (i + 1) + "]");
                    answerClick = answerClick.replaceAll('{src}', answerItem["url"]);

                    finalAnswer = finalAnswer.replaceAll('{answerClick}', answerClick);
                    if (result.answer.answers.length - 1 == i) {
                        finalAnswer += "。";
                    } else {
                        finalAnswer += "，";
                    }
                }
            }
        }
        if (finalAnswer == "") {
            let answer = Chat.PageApp.ServerConfig?.noAnswerReply ? Chat.PageApp.ServerConfig?.noAnswerReply:Text["chatNotFound"];
            let content = MessageController.text(answer);
            Chat.Chat_answer = JSON.stringify({ text: answer, type: "text" });
            Chat.Chat_answerType = "text";
            Chat.Chat_dataSource = "noAnswer";
            MessageController.doAddMessage(content, MessageController.LEFT);
        } else {
            let showContent = {
                needSpeechSynthesis: true,
                content: finalAnswer,
                sources: sources,
                relateds: relates,
                inputQuestion: Chat.Chat_inputQuestion,
            }
            ActionManager.isDoAddHistory = true;
            Chat.Chat_answerType = answerType;
            MessageController.doAddMessage(MessageController.websearchtext(showContent, messageId), MessageController.LEFT);
        }
    },
    doDisaplayKeyWordCard(list) {
        let FQACardAnswer = [];
        let maxRefer = parseInt(Chat.PageApp.ServerConfig.referMax);
        let referCount=0;
        for (let i = 0; i < list.length; i++) {
            if(referCount === maxRefer)break;
            let answer = list[i];
            let item = {
                "FDisplayText": answer,
                "ValueContent": "",
                "FCode": "keyWord",
                "Option": Chat.DATA_QA,
                "FName": answer,
                "FShowText": answer
            };
            referCount++
            FQACardAnswer.push(item);
        }
        let cardAnswer = {
            "FQACardColumn": [
                {
                    "FQACardAnswer": FQACardAnswer,
                    "thumbnailImageUrl": "",
                    "FMsgAnswer": Text["referDesc"],
                    "type": "Cards",
                    "title": Text["referTitle"],
                }
            ],
            "type": "Cards",
        };
        QbiAnswer.doDisplayAnswer(null, JSON.stringify(cardAnswer),null);
        Chat.PageApp.LOCK_SEND = false;
    },

    _initConfig(callback) {
        if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
            console.log('[MessageController _initConfig] ecp mode.');

            // Copilot存在ECP底下
            Config.IS_ECP_MODE = true;
            console.log('[MessageController _initConfig] update IS_ECP_MODE = ' + Config.IS_ECP_MODE);

            Config.ECP_URL = parent.parent.Utility.getBaseUrl();
            console.log('[MessageController _initConfig] update ECP_URL = ' + Config.ECP_URL);

            let userInfoData = parent.parent.CommonBusiness.getCurrentUser();
            userInfoData.mode = 'employee';
            CommonUtil.setLocalStorage('UserInfo', JSON.stringify(userInfoData));
            console.log('[MessageController _initConfig] set userInfo success.');

            HttpQuery.doRequest('/openapi/copilot/getCopilotInfo', { userId: userInfoData.userId }, function (result) {
                console.log('[MessageController _initConfig] isCopilotEnable = ' + result.enable);
                if (result.enable) {
                    parent.parent.$('#' + parent.parent.EcpController.KM_COPILOT_ICON_ID).show(250);
                    if (!(result.iconUrl == "" || result.iconUrl == null)) {
                        let userIcon = Config.ECP_URL + '/' + result.iconUrl;
                        parent.Main.PageApp.UserInfo.userIcon = userIcon;
                    }
                    parent.Main.PageApp.UserInfo.userName = userInfoData.userName;
                    parent.Main.PageApp.UserInfo.userMode = userInfoData.mode;
                    parent.Main.PageApp.isLogin = true;
                    let Language = result?.language?.toLowerCase();
                    sessionStorage.setItem("languageChange",Language);
                    if (!result.isReady) {
                        //切換語系判斷
                        MessageController.changeLanguage(Language);

                        MessageController.doCopilotNotReady();
                    } else {
                        //切換語系判斷
                        MessageController.changeLanguage(Language);

                        Chat.PageApp.ServerConfig = result;
                        Chat.PageApp.QbiCopilotInfo = result;
                        CommonUtil.setLocalStorage('QbiCopilotInfo', JSON.stringify(result));
                        MessageController._initShowMore();
                        MessageController._initTTS();
                        ChatEvent.init(result);
                        CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"loadHistoryList" ,{});
                        MessageController.doShowGreeting();
                        callback();
                    }
                }
            });
        } else {
            console.log('[MessageController _initConfig] plugin mode.');
            // Copilot獨立存在
            if (Config.COPILOT_ENABLE) {
                parent.parent.$('#' + parent.parent.EcpController.KM_COPILOT_ICON_ID).show(250);
                Chat.PageApp.ServerConfig = CommonUtil.getLocalStorage('QbiCopilotInfo');
                Chat.PageApp.QbiCopilotInfo = CommonUtil.getLocalStorage('QbiCopilotInfo');
                ChatEvent.init(CommonUtil.getLocalStorage('QbiCopilotInfo'));
                CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"loadHistoryList" ,{});
                MessageController.doShowGreeting();
                callback();
            }
        }
    },

    changeLanguage(Language){
        //ECP語系判讀
        if(sessionStorage?.getItem("language") !== sessionStorage?.getItem("languageChange") ){
            sessionStorage.setItem("language",Language);
            const kmCoPilot = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_ID);
            const kmCoPilotWeb = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_WEB_ID);
            const kmCoPilotHistory = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_HISTORY_ID);
            
            kmCoPilotHistory.contentWindow.location.reload();
            kmCoPilot.contentWindow.location.reload();
            kmCoPilotWeb.contentWindow.location.reload();
        }
    },
    
    doCopilotNotReady() {
        $('#sendMessageBox').attr('disabled', 'disabled');
        parent.Main.doNotReady();
    },

    _goChatBottom() {
        clearTimeout(MessageController.goChatBottomTimeout);
        MessageController.goChatBottomTimeout = setTimeout(() => {
          const el = $(MessageController.TARGET);
          el.animate({ scrollTop: el[0].scrollHeight }, 1000);
          hljs.highlightAll();
        }, 300);
    },

    _initShowMore() {
        let info = CommonUtil.getLocalStorage('QbiCopilotInfo');
        if (info != null) {
            let showMore = info.showMore;
            console.log('[MessageController _initShowMore] showMore = ' + showMore);
            if (!showMore) {
                document.styleSheets[0].insertRule(".ViewMoreText { display: none;}", 0);
            }
        }
    },
    _initTTS() {
        let info = CommonUtil.getLocalStorage('QbiCopilotInfo');
        if (info != null) {
            let TTSEnable = info.isTTSEnable;
            console.log('[MessageController _initTTSEnable] TTSEnable = ' + TTSEnable);
            if (!TTSEnable) {
                document.styleSheets[0].insertRule(".SpeakMessageVolume { display: none;}", 0);
            }
        }
    },
    
  escapeHTML(str) {
    if (str === undefined) {
        return str;
    }
    return str.replace(/[&<>"']/g, function (match) {
    return {
        "&": "&",
        "<": "<",
        ">": ">",
        '"': '"',
        "'": "'",
    }[match];
    });
  },
  updateMessage(messageCount, content, isFirstChunk , ResultType) {
    const targetId = `Message_${messageCount}`;
    let $target = $(`#${targetId}`);
    //console.log(`更新氣泡：${targetId}, 內容：${content}, 首次：${isFirstChunk}`);
    if (isFirstChunk) {
      MessageController.parser = null;
      //console.log("isFirstChunk=" + isFirstChunk + " messageCount=" + messageCount);
      let html = `<div id='Message_${messageCount}' class='ChatMessage ${MessageController.LEFT}'>${content}</div>`;
      $(MessageController.TARGET).append(html);
      MessageController.COUNT++;
      requestAnimationFrame(() => {MessageController._goChatBottom();});
      MessageController.cleanNoticeInterval();
      MessageController.setNoticeInterval(html);
      if (
        html.indexOf("satisfactionBtnDislike") !== -1 &&
        Config.CUSTOM_CSS_ENABLE
      ) {
        let dislike = $(".satisfactionBtnDislike").last();
        let ok = $(".satisfactionBtnOk").last();
        let like = $(".satisfactionBtnLike").last();
        let copy = $(".satisfactionBtnCopy").last();
        dislike[0].src = "../../image/custom-style-01/dislike_custom.png";
        ok[0].src = "../../image/custom-style-01/ok_custom.png";
        like[0].src = "../../image/custom-style-01/like_custom.png";
        copy[0].src = "../../image/custom-style-01/copy_custom.png";
      }
    }else if(ResultType === "handleSpeakMessageVolume"){
        let $speakBtn = $target.find('.SpeakMessageVolume');
        $speakBtn.attr('onclick', `WebSpeechSynthesis.speak('${MessageController.escapeHTML(content)}')`);
    }
    else if(ResultType === "innerSource") {
        let $messageText = $target.find(".MessageText font.WordBreakAll");
        content.forEach(item => {
            MessageController.insertTemplateAfterText({
                element: $messageText[0],
                targetText: item.answer,
                template: item.content
            });
        });
    }else if(ResultType === "isKmNoAnswer"){
        MessageController.cleanNoticeInterval();
        Chat.PageApp.NoticeContent = {
            noticeId: CommonUtil.getRandomUuid(),
            content: Text['streamAnswering']
        }
        MessageController.doAddMessage(MessageController.notice(Chat.PageApp.NoticeContent), MessageController.LEFT);
    } else {
      let $messageText = $target.find(".MessageText font.WordBreakAll");
      if ($messageText.length) {
        let formattedContent = MessageController.escapeHTML(content);
        formattedContent = (formattedContent === undefined ? "" : formattedContent).replace(/\n/g, "<br/>");
        if(MessageController.parser == null){
            MessageController.parser = new StreamHtmlParser($messageText[0]);
        }
        MessageController.parser.parse(formattedContent);
        // $messageText[0].innerHTML += formattedContent;
        // let currentContent = $messageText[0].innerHTML;
        // currentContent = currentContent.replace(
        //   /(\(\[\[\d+\]\]\)|\[\[\d+\]\])/g,
        //   ""
        // );
        // $messageText[0].innerHTML = currentContent;
        requestAnimationFrame(() => {MessageController._goChatBottom();});
      } else {
        let $fallbackText = $target.find(".MessageText");
        if ($fallbackText.length) {
          let formattedContent = MessageController.escapeHTML(content);
          formattedContent = (formattedContent === undefined ? "" : formattedContent).replace(/\n/g, "<br/>");
          $fallbackText.append(
            `<font class="WordBreakAll">${formattedContent}</font>`
          );
          let $newMessageText = $fallbackText.find("font.WordBreakAll");
          if ($newMessageText.length) {
            let currentContent = $newMessageText[0].innerHTML;
            currentContent = currentContent.replace(
              /(\(\[\[\d+\]\]\)|\[\[\d+\]\])/g,
              ""
            );
            $newMessageText[0].innerHTML = currentContent;
          }
          requestAnimationFrame(() => {MessageController._goChatBottom();});
        } else {
          console.error(`未找到 .MessageText in ${targetId}`);
        }
      }
    }
  },
  insertTemplateAfterText({ element, targetText, template }) {
    if (!element || !targetText || !template) {
        console.error("參數不完整");
        return;
    }

    // 先找有包含 targetText 的 text node
    let textNode = Array.from(element.childNodes).find(
        node => node.nodeType === Node.TEXT_NODE && node.textContent.includes(targetText)
    );

    if (textNode) {
        // 原本的插入流程
        const index = textNode.textContent.indexOf(targetText);
        const afterIndex = index + targetText.length;
        const before = textNode.textContent.slice(0, afterIndex);
        const after = textNode.textContent.slice(afterIndex);

        const beforeNode = document.createTextNode(before);
        const afterNode = document.createTextNode(after);

        const templateContainer = document.createElement("div");
        templateContainer.innerHTML = template;
        const templateNode = templateContainer.firstElementChild;

        element.replaceChild(afterNode, textNode);
        element.insertBefore(templateNode, afterNode);
        element.insertBefore(beforeNode, templateNode);
        return;
    }

    // 如果找不到，找最後一個 text node
    const allTextNodes = Array.from(element.childNodes).filter(
        node => node.nodeType === Node.TEXT_NODE
    );
    if (allTextNodes.length > 0) {
        const lastTextNode = allTextNodes[allTextNodes.length - 1];
        // 新邏輯：targetText 有包含最後一個 text node 的內容
        let newtargetText = CommonUtil.stripHTML(targetText,true).trim();
        let lastTextNodeText = CommonUtil.stripHTML(lastTextNode.textContent,true).trim();
        if (newtargetText.includes(lastTextNodeText)) {
            const templateContainer = document.createElement("div");
            templateContainer.innerHTML = template;
            const templateNode = templateContainer.firstElementChild;

            // 插入在最後一個 text node 之後
            if (lastTextNode.nextSibling) {
                element.insertBefore(templateNode, lastTextNode.nextSibling);
            } else {
                element.appendChild(templateNode);
            }
            return;
        }
    }

    // 如果還是找不到
    console.warn(`找不到文字：${targetText}`);
},
cleanJsonBlock(str) {
return str
  .replace(/^```json\s*/i, '')  // 移除開頭 ```json（忽略大小寫）
  .replace(/```$/, '')          // 移除結尾 ```
  .trim();                      // 去掉多餘空白
}

}
class StreamHtmlParser {
  constructor(targetElement) {
    this.currentNode = targetElement;
    this.htmlBuffer = "";
    this.textBuffer = "";
    this.inTag = false;
    this.isPotentialTag = false;
  }
  flushTextBuffer() {
    if (this.textBuffer) {
      const textNode = document.createTextNode(this.textBuffer);
      this.currentNode.appendChild(textNode);
      this.textBuffer = "";
    }
  }
  processTag() {
    if (this.htmlBuffer.startsWith("</")) {
      if (this.currentNode.parentNode) {
        this.currentNode = this.currentNode.parentNode;
      }
    } else if (this.htmlBuffer.endsWith("/>")) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = this.htmlBuffer;
      const newElement = tempDiv.firstChild;
      if (newElement) {
        this.currentNode.appendChild(newElement);
      }
    } else {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = this.htmlBuffer;
      const newElement = tempDiv.firstChild;
      if (newElement) {
        this.currentNode.appendChild(newElement);
        this.currentNode = newElement;
      }
    }
    this.htmlBuffer = "";
  }
  parse(chunk) {
    let processedChunk = "";
    let i = 0;
    while (i < chunk.length) {
      if (chunk[i] === "\\") {
        if (i + 1 < chunk.length && chunk[i + 1] === "n") {
          processedChunk += "<br/>";
          i += 2;
        } else {
          let slashCount = 1;
          i++;
          while (i < chunk.length && chunk[i] === "\\") {
            slashCount++;
            i++;
          }
          if (i < chunk.length && chunk[i] === "n") {
            i++;
          }
        }
      } else {
        processedChunk += chunk[i];
        i++;
      }
    }
    for (const char of processedChunk) {
      if (this.inTag) {
        this.htmlBuffer += char;
        if (char === ">") {
          this.processTag();
          this.inTag = false;
        }
      } else if (this.isPotentialTag) {
        if (/[a-zA-Z\/]/.test(char)) {
          this.inTag = true;
          this.isPotentialTag = false;
          this.htmlBuffer += char;
        } else {
          this.textBuffer += this.htmlBuffer + char;
          this.htmlBuffer = "";
          this.isPotentialTag = false;
        }
      } else if (char === "<") {
        this.flushTextBuffer();
        this.isPotentialTag = true;
        this.htmlBuffer = "<";
      } else {
        this.textBuffer += char;
      }
    }
    this.flushTextBuffer();
  }
  end() {
    if (this.isPotentialTag) {
      this.textBuffer += this.htmlBuffer;
      this.htmlBuffer = "";
    }
    this.flushTextBuffer();
  }
}