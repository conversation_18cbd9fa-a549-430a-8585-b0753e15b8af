<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 載入必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>

    <!-- 登入模組 -->
    <script src="../../ecp/EcpMemberLogin.js" charset="utf-8"></script>
    <script src="../../ecp/EcpUserLogin.js" charset="utf-8"></script>

</head>

<body onload="Login.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="./login.js" charset="utf-8"></script>
    <link rel=stylesheet type="text/css" href="./login.css" charset="utf-8">

    <div id="App">
        <section class="vh-100 mt-xl-3">
            <div class="container py-5 h-100">
                <div class="row d-flex justify-content-center align-items-center h-100">
                    <div class="col-12 col-md-8 col-lg-6 col-xl-5">
                        <div class="card-body p-5 text-center">
                            <img src="../../image/icon.png" width="125px">
                        </div>
                        <div style="padding-left: 10%;padding-right: 10%;">
                            <div class="form-outline mb-4">
                                <label class="form-label" for="account">{{T['account']}}</label>
                                <input type="text" id="account" class="form-control form-control-lg"
                                    v-bind:placeholder="T['account_desc']" v-model="account" />
                            </div>
                            <div class="form-outline mb-4">
                                <label class="form-label" for="pd">{{T['pd']}}</label>
                                <input type="password" id="pd" class="form-control form-control-lg"
                                    v-bind:placeholder="T['pd_desc']" v-model="pd" />
                            </div>
                            <div class="form-outline mb-2">
                                <label class="form-label" for="languageSelect">{{T['dropDownI18nText_Select']}}</label>
                                <select id="languageSelect" class="form-control form-control-lg" v-model="selectedLanguage" v-on:change="changeLanguage(selectedLanguage)">
                                    <option v-for="(lang, index) in i18n_langArray" :key="index" :value="lang">{{ languageTextMap[lang] }}</option>
                                </select>
                            </div>
                            <br>
                            <br>
                            <button v-if="UserLogin" class="btn btn-primary btn-lg btn-block loginBtn" type="button"
                                v-on:click="doLogin('employee')">{{T['Userlogin']}}</button>
                            <button v-if="MemberLogin" class="btn btn-primary btn-lg btn-block loginBtn" type="button"
                                v-on:click="doLogin('member')">{{T['Memberlogin']}}</button>
                            <button v-if="AnonymousLogin" class="btn btn-primary btn-lg btn-block loginBtn"
                                type="button" v-on:click="doLogin('anonymous')">{{T['Anonymous']}}</button>
                                <br>
                                <br>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>

</html>