::-webkit-scrollbar {
    width: 7px;
}

::-webkit-scrollbar-button {
    background: transparent;
    border-radius: 4px;
}

::-webkit-scrollbar-track-piece {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(139, 168, 217, .7);
    border: 1px solid slategrey;
}

::-webkit-scrollbar-track {
    box-shadow: transparent;
}

/* Loading */
.progressBar {
    background-color: rgb(139, 168, 217);
}

.progressDiv {
    position: fixed;
    top: 45%;
    width: 80%;
    padding: 50px;
    background-color: rgba(222, 231, 245);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    left: 10%;
}

/* 響應式設計 - 新版 UI */
@media screen and (max-width: 768px) {
    .history-panel {
        display: none !important;
    }

    .chatbot-container {
        width: 100% !important;
    }

    .crm-area {
        display: none;
    }

    body {
        flex-direction: column;
    }

    .chat-bubble {
        max-width: 80%;
    }

    .sendMessage {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }
}