var WebView = {

    MODE_HTML: 'HTML',
    MODE_URL: 'URL',
    MODE_LIST: 'LIST',

    doLoad() {
        this.initApp();
        if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
            console.log('[WebView _initConfig] ecp mode.');

            // Copilot存在ECP底下
            Config.IS_ECP_MODE = true;
            console.log('[WebView _initConfig] update IS_ECP_MODE = ' + Config.IS_ECP_MODE);

            Config.ECP_URL = parent.parent.Utility.getBaseUrl();
            console.log('[WebView _initConfig] update ECP_URL = ' + Config.ECP_URL);
        }
    },

    initApp() {
        WebView.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                WebTitle: 'KM WebView Title',
                HtmlView: '',
                WebViewSrc: '',
                Mode: WebView.MODE_HTML,
                attachments: [],
                InputQuestion: '',
                ListData: [],
                IsBackShow: false,
            },
            methods: {
                getFileNameWithoutExt(fullName) {
                  return fullName.split('.').slice(0, -1).join('.');
                },
                doCloseView(event) {
                    WebView.doCloseView();
                },
                downloadFile(id,fileName,preview) {
                    ChatEvent.onSourceClick(id, fileName, "file","",preview);
                },
                doOpenDetail(type, content, sourceId) {
                    ChatEvent.onSourceClick(sourceId, content, type).then(()=>{
                        WebView.PageApp.IsBackShow = true;
                    });
                },
                doBackList() {
                    WebView._doInitWebViewStatus();
                    WebView.PageApp.WebTitle = Text['viewMore'];
                    WebView.PageApp.Mode = WebView.MODE_LIST;
                },
            },
            watch: {},
        });
    },

    setContent(title, result, mark) {
        ChatEvent.init(CommonUtil.getLocalStorage('QbiCopilotInfo'));
        if (!parent.EcpController.SHOW_WEB_TOGGLE) {
            setTimeout(() => {
                parent.EcpController.doWebViewToggle();
            }, 500);
        }
        result.content = result.content.replaceAll("<br />", "<br>");
        if (mark != undefined && mark != null && mark.length > 0) {
            // if (result.content.indexOf(mark) > 0) {
            //     result.content = result.content.replaceAll(mark, "<div class='mark'>" + mark + "</div>");
            //     console.log('[WebView setContent] mark success.');
            // }
            parseDom = new DOMParser();
            let markXpaths = document.evaluate("//text()", parseDom.parseFromString(mark, "text/html"));
            markTextArray = [];
            while (markXpaths) {
                markXpath = markXpaths.iterateNext();
                if (!!!markXpath) {
                    break;
                }
                markText = markXpath.textContent.trim();
                if (markText.length > 0) {
                    markTextArray.push(markText);
                }
            }
            // if(markTextArray.length>0){
            //     firstXpaths = document.evaluate("//*[contains(text(),'"+ markTextArray[0]+"')]", result.content)
            //     firstXpath = firstXpath.iterateNext();
            //     finalXpaths = firstXpath = document.evaluate("//*[contains(text(),'"+ markTextArray[markTextArray.length-1]+"')]", result.content)
            //     finalXpath = finalXpaths.iterateNext();
            //     result.content = result.content.replace(firstXpath.textContent, "<mark>" + firstContent.textContent).replace(finalXpath.textContent, "<mark>" + finalXpath.textContent + "</mark>>")
            // }

            resultContent = parseDom.parseFromString(result.content, "text/html");
            for (i = 0; i < markTextArray.length; i++) {
                markText = markTextArray[i];
                if (markText.indexOf("\n")) {
                    _markText = markText.split("\n");
                    for (x = 0; x < _markText.length; x++) {
                        try {
                            xpathObjects = document.evaluate("//*[contains(text(),'" + _markText[x] + "')]", resultContent)
                        } catch (error) { }
                        xpathObject = xpathObjects.iterateNext();
                        if (!!!xpathObject) {
                            continue;
                        }
                        result.content = result.content.replace(xpathObject.innerHTML, "<mark>" + xpathObject.innerHTML)
                    }

                } else {
                    try {
                        xpathObjects = document.evaluate("//*[contains(text(),'" + markText + "')]", resultContent)
                    } catch (error) { }
                    xpathObject = xpathObjects.iterateNext();
                    if (!!!xpathObject) {
                        continue;
                    }
                    result.content = result.content.replace(xpathObject.innerHTML, "<mark>" + xpathObject.innerHTML)
                }
            }

        }
        WebView.PageApp.WebTitle = title;
        WebView.PageApp.HtmlView = CommonUtil.stripHTML(result.content, true).length == 0 ? Text['knowledgeEmpty'] : result.content;
        WebView.PageApp.Mode = WebView.MODE_HTML;
        WebView.PageApp.attachments = result.attachments;
        WebView._doInitWebViewStatus();
        parent.EcpController.setWebViewPosition();
    },

    setWebView(title, url, mark,isExternal) {
        ChatEvent.init(CommonUtil.getLocalStorage('QbiCopilotInfo'));
        if (!parent.EcpController.SHOW_WEB_TOGGLE) {
            setTimeout(() => {
                parent.EcpController.doWebViewToggle();
            }, 500);
        }
        WebView.PageApp.WebTitle = title;
        WebView.PageApp.WebViewSrc = url;
        WebView.PageApp.Mode = WebView.MODE_URL;
        WebView._doInitWebViewStatus();
        parent.EcpController.setWebViewPosition();
        if(!isExternal){
            setTimeout(function () {
                try {
                    let content = document.getElementById('mainWebFrame').contentWindow.document.getElementsByClassName('HtmlContent')[0].innerHTML;
                    // if (content.indexOf(mark) > 0) {
                    // content = content.replaceAll(mark, "<div class='mark'>" + mark + "</div>");
                    // document.getElementById('mainWebFrame').contentWindow.document.getElementsByClassName('HtmlContent')[0].innerHTML = content;
                    // console.log('[WebView setWebView] mark success.');
                    // }
                    parseDom = new DOMParser();
                    let markXpaths = document.evaluate("//text()", parseDom.parseFromString(mark, "text/html"));
                    markTextArray = [];
                    while (markXpaths) {
                        markXpath = markXpaths.iterateNext();
                        if (!!!markXpath) {
                            break;
                        }
                        markText = markXpath.textContent.trim();
                        if (markText.length > 0) {
                            markTextArray.push(markText);
                        }
                    }
                    // if(markTextArray.length>0){
                    // resultContent = parseDom.parseFromString(content,  "text/html");
                    // firstXpaths = document.evaluate("//*[contains(text(),'"+ markTextArray[0]+"')]", resultContent)
                    // firstXpath = firstXpaths.iterateNext();
                    // finalXpaths = document.evaluate("//*[contains(text(),'"+ markTextArray[markTextArray.length-1]+"')]", resultContent)
                    // finalXpath = finalXpaths.iterateNext();
                    // content = content.replace(firstXpath.textContent, "<mark>" + firstXpath.textContent).replace(finalXpath.textContent, "<mark>" + finalXpath.textContent + "</mark>")
                    // document.getElementById('mainWebFrame').contentWindow.document.getElementsByClassName('HtmlContent')[0].innerHTML = content;
                    // }
                    resultContent = parseDom.parseFromString(content, "text/html");
                    for (i = 0; i < markTextArray.length; i++) {
                        markText = markTextArray[i];
                        if (markText.indexOf("\n")) {
                            _markText = markText.split("\n");
                            for (x = 0; x < _markText.length; x++) {
                                try {
                                    xpathObjects = document.evaluate("//*[contains(text(),'" + _markText[x] + "')]", resultContent)
                                } catch (error) { }
                                xpathObject = xpathObjects.iterateNext();
                                if (!!!xpathObject) {
                                    continue;
                                }
                                content = content.replace(xpathObject.innerHTML, "<mark>" + xpathObject.innerHTML)
                            }
                        } else {
                            try {
                                xpathObjects = document.evaluate("//*[contains(text(),'" + markText + "')]", resultContent)
                            } catch (error) { }
                            xpathObject = xpathObjects.iterateNext();
                            if (!!!xpathObject) {
                                continue;
                            }
                            content = content.replace(xpathObject.innerHTML, "<mark>" + xpathObject.innerHTML)
                        }
                    }
                    document.getElementById('mainWebFrame').contentWindow.document.getElementsByClassName('HtmlContent')[0].innerHTML = content;
                } catch (e) {
                    console.error('[setWebView] mark error:' + e);
                }
            }, 1500);
        }
    },

    setListView(title, inputQuestion, data) {
        ChatEvent.init(CommonUtil.getLocalStorage('QbiCopilotInfo'));
        if (data != null && data != undefined) {
            if (!parent.EcpController.SHOW_WEB_TOGGLE) {
                setTimeout(() => {
                    parent.EcpController.doWebViewToggle();
                }, 500);
            }
            WebView.PageApp.WebTitle = title;
            WebView.PageApp.Mode = WebView.MODE_LIST;
            for (let i = 0; i < data.length; i++) {
                let item = data[i];
                item.page_content = CommonUtil.stripHTML(item.page_content, false);
                item.page_content = item.page_content.replaceAll('\r', '');
                item.page_content = item.page_content.replaceAll('\n', '<br>');
                data[i] = item;
            }
            WebView.PageApp.ListData = data;
            WebView.PageApp.InputQuestion = inputQuestion;
            WebView._doInitWebViewStatus();
            parent.EcpController.setWebViewPosition();
        }
    },

    doCloseView() {
        parent.EcpController.doWebViewToggle();
    },

    _doInitWebViewStatus() {
        WebView.PageApp.IsBackShow = false;
    },
}

parent.Window.QbiCopilotWebView = WebView;