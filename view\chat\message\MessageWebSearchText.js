var MessageWebSearchText = {

    template: `
    <div class="MessageWebSearchText">
            <div class="MessageText">
                <font class="Copy-{copyId} WordBreakAll">
                    {content}
                </font>
                <div style="padding-top: 10px;">
                    <font style="color:rgb(0, 88, 176)">{searchSourceText}</font>
                    <br>
                    <div id="source">
                        {sources}
                    </div>
                    <font style="color:rgb(234, 107, 102)">{gptAlert}</font>
                </div>
            </div>
    `,

    template_ViewMore: `
    <div class="MessageWebSearchText">
            <div class="MessageText">
                <font class="Copy-{copyId} WordBreakAll">
                    {content}
                </font>
                <div style="padding-top: 10px;">
                    <font style="color:rgb(0, 88, 176)">{searchSourceText}</font>
                    <br>
                    <div id="source">
                        {sources}
                    </div>
                    <div class="ViewMoreContainer">
                        <span class="ViewMoreText">
                            <a href="#{messageId}"
                                onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
                        </span>
                    </div>
                    <div style="display: inline-block;width:100%"><font style="color:rgb(234, 107, 102); font-size: smaller;">{gptAlert}</font></div>
                </div>
            </div>
    `,
    template_speechSynthesis:
        `
        <div>
            {timestamp}
            <div class="satisfactionBar animate__animated animate__fadeInUp">
            <img class="SpeakMessageVolume"  title="{SpeakMessageVolumeHint}"  src="../../image/SingalSpeech_.png"
                onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">
            </div>        
        </div>
        </div>
    `,

    template_end: `
            <div>
                {relateds}
            </div>
        `,

    template_noMessage: `
    <div class="MessageText">
        <font class="Copy-{copyId} WordBreakAll">{content}</font>
    </div>
    `,

    template_noMessageViewMore: `
    <div class="MessageWebSearchText">
        <div class="MessageText">
            <font class="Copy-{copyId} WordBreakAll">{content}</font>
            <div class="ViewMoreContainer">
                <span class="ViewMoreText">
                    <a href="#{messageId}"
                        onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
                </span>
            </div>
        </div>
    </div>
    `,

    create(args, messageId) {
        let timestamp = Chat.Chat_HistoryTimestamp || CommonUtil.formatStringDate(new Date()); 
        if (typeof args == 'object') {

            let data = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY)[messageId];

            let html =
                (data == null ? MessageWebSearchText.template : MessageWebSearchText.template_ViewMore) +
                MessageWebSearchText.template_speechSynthesis +
                MessageWebSearchText.template_end;

            html = html.replaceAll('{content}', args.content);

            let copyContent = CommonUtil.stripHTML(args.content, true);
            MessageController.TotalMessage_Synthesis += copyContent;

            html = html.replaceAll('{timestamp}', timestamp);

            html = html.replace('{searchSourceText}', Text['searchSourceText']);
            html = html.replaceAll('{messageId}', messageId);
            html = html.replaceAll('{viewMore}', Text['viewMore']);
            html = html.replaceAll('{inputQuestion}', args.inputQuestion.replace(/'/g, "\\'"));
            html = html.replace('{gptAlert}', Chat.PageApp.ServerConfig?.webSearchGenerateText==null?Text['gptAlert']:Chat.PageApp.ServerConfig.webSearchGenerateText);

            html = html.replaceAll('{SpeakMessageVolumeHint}', Text['SpeakMessageVolumeHint']);
            html = html.replace('{sources}', MessageWebSearchText.getSources(args.sources));
            // html = html.replace('{relateds}', MessageWebSearchText.getRelateds(args.relateds));
            html = html.replace('{relateds}', "");
            html = html.replaceAll('{speechSynthesisString}', CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
            return html;
        } else {
            let html = (messageId == null ? MessageWebSearchText.template_noMessage : MessageWebSearchText.template_noMessageViewMore);

            html += Chat.PageApp.QbiCopilotInfo.isTTSEnable ? MessageWebSearchText.template_speechSynthesis : MessageWebSearchText.template_timestamp;
            let orignal = args;
            if (CodeParse.isContainCode(orignal)) {
                args = CodeParse.getContentParse(orignal);
            } else {
                args = args.replaceAll('\\n', '<br>');
                args = args.replaceAll('\n\n', '\n');
                args = args.replaceAll('\n', '<br>');
            }
            html = html.replace('{content}', args);
            MessageController.TotalMessage_Synthesis += args;

            html = html.replaceAll('{timestamp}', timestamp);
            html = html.replaceAll('{messageId}', messageId);
            html = html.replaceAll('{speechSynthesisString}', MessageController.escapeHTML(args));
            html = html.replaceAll('{viewMore}', Text['viewMore']);
            html = html.replaceAll('{inputQuestion}', Chat.Chat_inputQuestion.replace(/'/g, "\\'"));
            return MessageWebSearchText._setContentStyle(args, html);;
        }
    },

    getSources(sources) {
        result = "";
        for (let i = 0; sources.length > i; i++) {
            let source = sources[i];
            result += source
        }
        return result;
    },
    getRelateds(relateds) {
        let result = "";
        let template = `<div class="GptRelated" onclick="ChatEvent.onRelatedClick('{content}')" title="{answer}">{content}</div>`;
        let template_noAnswer = `<div class="GptRelated" onclick="ChatEvent.onRelatedClick('{content}')">{content}</div>`;
        if (relateds.length > 0) {
            MessageController.TotalMessage_Synthesis += Text["relatedPreWord"];
        }
        for (let i = 0; i < relateds.length; i++) {
            let item = relateds[i];
            let isHasAnswer = item.hasOwnProperty('follow-up_answer');
            let resultItem = isHasAnswer ? template : template_noAnswer;
            resultItem = resultItem.replaceAll('{content}', item['follow-up_question']);
            MessageController.TotalMessage_Synthesis += item['follow-up_question'];
            if (isHasAnswer) {
                resultItem = resultItem.replaceAll('{answer}', item['follow-up_answer']);
            }
            result += resultItem;
        }
        return result;
    },

    _setContentStyle(content, html) {
        if (content.length >= MessageText.lengthCheck) {
            html = html.replaceAll('{contentStyle}', 'style="width:100%"');
        } else {
            html = html.replaceAll('{contentStyle}', '');
        }
        return html;
    },
}
