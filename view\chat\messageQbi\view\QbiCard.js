var QbiCard = {
  TEMPLATE: `
        <swiper-container class="mySwiper" pagination="true" pagination-clickable="true"
            navigation="false" space-between="30" loop="true" slides-per-view="auto" effect="coverflow">
                   {SWIPER_TEMPLATE}
        </swiper-container>
    `,

  SWIPER_TEMPLATE: `
                <swiper-slide>
                        <div class="card" style="width: 100%">
                                  {IMAGE_TEMPLATE}
                                  <div class="card-body">
                                      <h5 class="card-title">{title}</h5>
                                      <p class="card-text">{desc}</p>
                                      <ul class="list-group list-group-flush">
                                          {CARD_ITEM_TEMPLATE}
                                      </ul>
                                  </div>
                        </div>
                </swiper-slide>
    `,

  CARD_ITEM_TEMPLATE: `
       <li class="list-group-item cardItem"><button type="button" class="btn btn-link cardBtn" onclick="ChatEvent.onAnswerButtonClick('{option}','{code}')" title="{content}">{content}</button></li>
    `,
  KEYWORD_CARD_ITEM_TEMPLATE: `
       <li class="list-group-item cardItem"><button type="button" class="btn btn-link cardBtn" onclick="ChatEvent.KeyWordsendMessage('{content}')" title="{content}">{content}</button></li>
    `,

  IMAGE_TEMPLATE: `
        <img class="{QbiCardImage} {QbiCardstyle}"
        src="{ImageSrc}"
        alt="Card image cap" onclick="{imageClickUrl}">
    `,

  create(answer) {
    let html = QbiCard.TEMPLATE;
    let cards = answer.FQACardColumn;
    let swiperHTML = "";
    for (let i = 0; i < cards.length; i++) {
      let card = cards[i];

      // 卡片標頭圖片
      let imageHTML = "";
      let thumbnailImageUrl = card.thumbnailImageUrl;
      if (
        !CommonUtil.isStringEmpty(thumbnailImageUrl) ||
        !CommonUtil.isStringEmpty(card.imageClickUrl)
      ) {
        imageHTML = QbiCard.IMAGE_TEMPLATE;
        imageHTML = imageHTML.replaceAll("{ImageSrc}", thumbnailImageUrl);
        if (card.imageClickUrl) {
          imageHTML = imageHTML.replaceAll(
            "{imageClickUrl}",
            "window.open('" + card.imageClickUrl + "')"
          );
          imageHTML = imageHTML.replaceAll("{QbiCardstyle}", "pointer");
        } else {
          imageHTML = imageHTML.replaceAll("{imageClickUrl}", "");
          imageHTML = imageHTML.replaceAll("{QbiCardstyle}", "");
        }

        if (answer.imageAspectRatio == "rectangle") {
          imageHTML = imageHTML.replaceAll(
            "{QbiCardImage}",
            "QbiCardImage_rectangle"
          );
        } else {
          imageHTML = imageHTML.replaceAll(
            "{QbiCardImage}",
            "QbiCardImage_square"
          );
        }
      }

      // 卡片按鈕
      let cardButtons = card.FQACardAnswer;
      let cardButtonHTML = "";
      if (
        cardButtons[0] &&
        cardButtons[0].FCode &&
        cardButtons[0].FCode === "keyWord"
      ) {
        for (let j = 0; j < cardButtons.length; j++) {
          let card_showText = cardButtons[j].FShowText;
          let card_item = QbiCard.KEYWORD_CARD_ITEM_TEMPLATE;
          card_item = card_item.replaceAll("{content}", card_showText);
          cardButtonHTML += card_item;
        }
      } else {
        for (let j = 0; j < cardButtons.length; j++) {
          let card_showText = cardButtons[j].FShowText;
          let card_displayText = cardButtons[j].FDisplayText;
          let card_option = cardButtons[j].Option;
          let card_item = QbiCard.CARD_ITEM_TEMPLATE;
          let card_code = "";
          if (card_option == "Url") {
            card_code = encodeURIComponent(cardButtons[j].FName);
          } else if (card_option == "Option") {
            card_code = encodeURIComponent(JSON.stringify({
              FCode: cardButtons[j].FCode,
              FDisplayText: card_displayText,
              FShowText: card_showText
            }))
          } else {
            card_code = cardButtons[j].FCode;
          }
          card_item = card_item.replaceAll("{content}", card_showText);
          card_item = card_item.replaceAll("{code}", card_code);
          card_item = card_item.replaceAll("{option}", card_option);
          cardButtonHTML += card_item;
        }
      }

      // 卡片標題及副標題
      let title = card.title;
      let desc = card.FMsgAnswer;

      // 組裝單一卡片
      let cardHTML = QbiCard.SWIPER_TEMPLATE;
      cardHTML = cardHTML.replaceAll("{title}", title);
      cardHTML = cardHTML.replaceAll("{desc}", desc);
      cardHTML = cardHTML.replaceAll("{IMAGE_TEMPLATE}", imageHTML);
      cardHTML = cardHTML.replaceAll("{CARD_ITEM_TEMPLATE}", cardButtonHTML);

      swiperHTML += cardHTML;
    }
    html = html.replaceAll("{SWIPER_TEMPLATE}", swiperHTML);
    return html;
  }
};
