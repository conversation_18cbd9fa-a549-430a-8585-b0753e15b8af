body {
    overflow-y: hidden;
    /* Hide vertical scrollbar */
    overflow-x: hidden;
    /* Hide horizontal scrollbar */
    font-size: 14px;
}
.messageBox {
    height: calc(100vh - 155px);
    display: flex;
    flex-direction: column;
}
.messageList {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-gutter: stable;
    z-index: 1;
}
#ToolZone {
    position: absolute;
    bottom: 50px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 38px;
    max-height: 85px;
    z-index: 2;
}
.catalog {
    width: 100%;
    background-color: rgb(198, 215, 255);
    text-align: center;
    cursor: pointer;
}
.catalogItem {
    background-color: rgb(139, 168, 217);
    color: white;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.catalogItems {
    overflow: scroll;
    height: 50vh
}

.catalogSelect {
    cursor: pointer;
    color: rgb(68, 130, 196);
    user-select: none;
}

.catalogText {
    font-size: 1rem;
}

.catalogContent {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 1rem;
}


.sendMessage {
    position: fixed;
    bottom: 0px;
    width: 100%;    
    background-color: rgb(139, 168, 217);
    z-index: 3;
}


.sendMessageDiv {
    margin: 0px 10px;
    border-radius: 6px;
    display: flex;
    background-color: rgb(255, 255, 255);
    border-radius: 6px ;
    border: rgb(193 193 193) solid 1px ;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0px;
    position: relative;
    width: 100%;
    min-height: 1px;
    transition: width 1s, height 1s;
}

.sendMessageBox {
    border: 3px;
    outline: none;
    width: calc(100% - 1.7rem);
    border-radius: 5px;
}

.MainsendMessageBox {
    position: unset ;
    flex: 1 ;
    display: flex ;
    align-items: flex-end ;
    flex-direction: column ;
    bottom: 1px;
    left: -21.5px;
    width: 93.56%;
}

.MainIconBox {
    width: 5%;
}

/* --------------麥克風圖示-------------- */
.microPhoneIconOn{
    position: unset ;
    width: 38px ;
    right: 10px;
    bottom: 10px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.microPhoneIconOff{
    position: unset ;
    width: 38px ;
    right: 10px;
    bottom: 10px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

/* --------------發送圖示-------------- */

.sendIcon {
    position: unset;
    cursor: pointer;
    width: 38px;
}
/* --------------外部搜尋圖示-------------- */
.knowledgeIconOn {
    width: 30px;
    margin-bottom: 5px;
    position: absolute;
    bottom: 0px;
    left: 5px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
.knowledgeIconOff {
    width: 30px;
    margin-bottom: 5px;
    position: absolute;
    bottom: 0px;
    left: 5px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

/* --------------清除主題圖示-------------- */

.iconcontroly{
    display: inline-grid;
}
.cleanIcon {
    height: 35.34px;
    width: 38px;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}
/* --------------發問任務圖示-------------- */
.catalogBtnUp {
    margin-bottom: 3px;
}
.catalogBtnDown {
    margin-bottom: 3px;
}



.ChatMessageTime {
    color: black;
}

.sendToken {
    color: white;
    padding-left: 25px;
}





.functionTitle {
    font-size: 1rem;
    color: white;
    transition: width 0.1s, height 0.1s;
}
/* 
.functionButton {
    margin-bottom: 5px;
    position: absolute;
    right: 2px;
    transition: width 0.1s, height 0.1s;
} */


.functionButton:hover {
    width: 35px;
}

.tokenText {
    color: black !important;
    bottom: 5px !important;
    position: unset;
    margin-right: 10px;
}

.audio-wave {
    position: absolute;
    height: 0px;
    margin-left: 13%;
    width: 65%;
    bottom: 70px;
    transition: width 0.1s, height 0.1s;
}
/*自定義*/
.SendBox {
    display: flex ;
    justify-content: space-between ;
    flex-direction: row ;
    margin: 5px 0px;
}


/*自定義*/
.message-info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    height: 100%;
    padding: 5px;
}
