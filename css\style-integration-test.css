/* 樣式整合測試文件 - 解決新舊樣式衝突 */

/* 確保新版 UI 樣式優先級 */
.chat-bubble.bot {
    background-color: #f1f1f1 !important;
    border-radius: 10px !important;
    padding: 10px 12px !important;
}

.chat-bubble.user {
    background-color: #d3ecff !important;
    border-radius: 10px !important;
    padding: 10px 12px !important;
}

/* 修復輸入框樣式衝突 */
.sendMessage {
    background-color: #ffffff !important;
    border-top: 1px solid #ccc !important;
}

.sendMessageDiv {
    background-color: rgb(255, 255, 255) !important;
    border: rgb(193 193 193) solid 1px !important;
    border-radius: 6px !important;
}

/* 確保 SVG 圖標正確顯示 */
.mainhistorytoggleIcon {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}

.catalogBtnUp {
    width: 20px;
    height: 20px;
}

/* 修復字體樣式 */
body, .chat-bubble, .catalogContent {
    font-family: "Noto Sans TC", Arial, sans-serif !important;
}

/* 確保響應式設計正常工作 */
@media screen and (max-width: 768px) {
    .history-panel {
        display: none !important;
    }
    
    .chatbot-container {
        width: 100% !important;
    }
    
    .messageBox {
        height: calc(100vh - 120px) !important;
    }
}

/* 修復 z-index 層級問題 */
.sendMessage {
    z-index: 1000 !important;
}

.messageList {
    z-index: 1 !important;
}

#ToolZone {
    z-index: 2 !important;
}

/* 確保新版按鈕樣式 */
.chat-actions-right button {
    background: #3182f6 !important;
    border: none !important;
    border-radius: 50% !important;
    color: white !important;
}

/* 修復滿意度按鈕與新樣式的兼容性 */
.satisfactionBar {
    margin-top: 10px;
}

.satisfactionBtn {
    margin-right: 8px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.satisfactionBtn:hover {
    opacity: 0.7;
}

/* 確保目錄樣式正常 */
.catalog {
    background-color: rgb(198, 215, 255) !important;
}

.catalogItem {
    background-color: rgb(139, 168, 217) !important;
    color: white !important;
}

/* 修復載入動畫樣式 */
.progressDiv {
    z-index: 9999 !important;
}

/* 確保用戶圖標樣式 */
.userIcon img {
    border-radius: 30px !important;
}

/* 修復導航欄樣式 */
.navBar {
    background-color: #1f4379 !important;
    color: white !important;
}

.title {
    color: white !important;
}

/* 確保歷史記錄樣式 */
.historyItem {
    padding: 10px 12px !important;
    border-bottom: 1px solid #e5e5e5 !important;
    cursor: pointer !important;
}

.historyItem:hover {
    background-color: #f5f5f5;
}

/* 修復自定義樣式兼容性 */
.isCustomCssEnable .chat-bubble.bot {
    background-color: rgb(245, 245, 245) !important;
}

.isCustomCssEnable .sendMessage {
    background-color: rgb(235, 235, 235) !important;
}

/* 確保動畫效果正常 */
.animate__animated {
    animation-duration: 0.5s !important;
}

/* 修復文字選擇樣式 */
.chat-bubble {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

/* 確保滾動條樣式一致 */
.messageList::-webkit-scrollbar,
.history-content::-webkit-scrollbar {
    width: 7px;
}

.messageList::-webkit-scrollbar-thumb,
.history-content::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(139, 168, 217, .7);
    border: 1px solid slategrey;
}
