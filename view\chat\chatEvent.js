var ChatEvent = {
  downloadUrl: "",
  Mode: "",
  SatisfactionRecord: [],

  init(args) {
    ChatEvent.Mode = CommonUtil.getLocalStorage("QbiCopilotMode");
    if (ChatEvent.Mode === "PLUGIN") {
      ChatEvent.downloadUrl =
        '/openapi/copilot/getKMFile/{fileName}?args=%7B"attachmentIds"%3A%5B"{entityId}"%5D,"type"%3A"{type}","preview"%3A{preview}%7D';
    } else {
      let downloadCode = args.downloadType;
      ChatEvent.downloadUrl =
        "/" +
        downloadCode +
        '.DownloadFile.file?pageCode=Ecp.Knowledge.View&args=%7B"attachmentIds"%3A%5B"{entityId}"%5D,"type"%3A"{type}","preview"%3A{preview}%7D';
    }
  },

  onSourceClick(sourceId, content, type, mark,preview) {
    return new Promise((resolve, reject) => {
      const timestamp = new Date().getTime(); // 時間戳避免快取
      let url;
      switch (type) {
        case "file":
          // 檔案下載
          url = ChatEvent.downloadUrl.replace("{fileName}", content).replace("{entityId}", sourceId).replace("{type}", type).replace("{preview}", !!preview) +
            "?timestamp=" +
            timestamp ;
          window.open(Config.ECP_URL + url);
          resolve(); // 檔案下載完立即 resolve
          break;
        case "document":
          // 檔案下載
          url =
            ChatEvent.downloadUrl
              .replace("{entityId}", sourceId)
              .replace("{fileName}", "")
              .replace("{type}", type) +
            "?timestamp=" +
            timestamp;
          window.open(Config.ECP_URL + url);
          resolve(); // 檔案下載完立即 resolve
          break;
        default:
          // 預設開啟
          let markContent = unescape(mark);

          if (ChatEvent.Mode === "PLUGIN") {
            HttpQuery.doRequest(
              "/openapi/copilot/getKMContent",
              { sourceId: sourceId },
              function (result) {
                try {
                  parent.parent.Window.QbiCopilotWebView.setContent(
                    content,
                    result,
                    markContent
                  );
                } catch (e) {
                  console.log(
                    "[ChatEvent-onSourceClick] teams not support parent."
                  );
                  WebView.setContent(content, result, markContent);
                }
                resolve();
              },
              function (error) {
                reject(error);
              }
            );
          } else {
            let title = content;
            url = '/Ecp.Knowledge.View.page?args=%7B"entityId"%3A"{entityId}"%2C"isListView"%3Atrue%7D';
            url = url.replace("{entityId}", sourceId) + "&timestamp=" + timestamp;
            try {
              parent.parent.Window.QbiCopilotWebView.setWebView(
                title,
                Config.ECP_URL + url,
                markContent
              );
              resolve();
            } catch (e) {
              reject(e);
            }
          }
          break;
      }
    });
  },

  onCopyClick(btn, copyId) {
    btn.className = "satisfactionBtnCopy animate__animated animate__tada";
    const containerClassName = btn.parentElement.parentElement.parentElement.className;
      //全格式複製
      let elementsToCopy = document.querySelectorAll(".Copy-" + copyId);
      // 創建一個臨時的 div 來存放要複製的所有內容
      let tempDiv = document.createElement("div");
      // 將所有內容加入到臨時 div 中
      elementsToCopy.forEach(function (copyElement) {
        let clone = copyElement.cloneNode(true); // 克隆元素

        // 檢查並移除"資料來源："開始的內容
        let dataSourceElements = clone.querySelectorAll('font[style*="color: rgb(0, 88, 176)"]');
        dataSourceElements.forEach(function(dataSourceElement) {
          if (dataSourceElement.textContent.includes('資料來源：')) {
            // 找到包含"資料來源："的元素，移除它及其後續所有內容
            let currentElement = dataSourceElement;
            while (currentElement && currentElement.nextSibling) {
              let nextSibling = currentElement.nextSibling;
              currentElement.parentNode.removeChild(nextSibling);
            }
            // 移除"資料來源："元素本身
            if (dataSourceElement.parentNode) {
              dataSourceElement.parentNode.removeChild(dataSourceElement);
            }
          }
        });

        // 檢查視頻元素
        let videoElement = clone.querySelector('video');
        if (videoElement) {
            let videoSource = videoElement.querySelector('source[type="video/mp4"]');
            if (!videoSource) {
                videoSource = videoElement;
            }
            let videoUrl = videoSource ? videoSource.src : 'Video URL not available';
        
            // 創建下載鏈接
            let videoLink = document.createElement('a');
            videoLink.href = videoUrl;
            videoLink.textContent = Text['video_CopyShowText'];
            videoLink.target = "_blank"; // 在新窗口打開
            videoLink.style.display = 'block'; // 使其顯示為 block
            videoLink.style.textAlign = 'center'; // 置中
        
            // 用下載鏈接替換視頻元素
            videoElement.parentNode.replaceChild(videoLink, videoElement); // 替換視頻元素
            tempDiv.appendChild(videoLink); // 將鏈接附加到臨時 div
            tempDiv.appendChild(document.createElement("br")); // 添加換行
        }
        
        // 檢查音頻元素
        let audioElement = clone.querySelector('audio');
        if (audioElement) {
            let audioSource = audioElement.querySelector('source[type="audio/mpeg"]');
            let audioUrl = audioSource ? audioSource.src : 'Audio URL not available';
        
            // 創建下載鏈接
            let audioLink = document.createElement('a');
            audioLink.href = audioUrl;
            audioLink.textContent = Text['audio_CopyShowText'];
            audioLink.target = "_blank"; // 在新窗口打開
            audioLink.style.display = 'block'; // 使其顯示為 block
            audioLink.style.textAlign = 'center'; // 置中
        
            // 用下載鏈接替換音頻元素
            audioElement.parentNode.replaceChild(audioLink, audioElement); // 替換音頻元素
            tempDiv.appendChild(audioLink); // 將鏈接附加到臨時 div
            tempDiv.appendChild(document.createElement("br")); // 添加換行
        }
        
        // 檢查文件下載元素
        let fileDownloadElement = clone.querySelector('.ChatMessageFileContentBottom a.Link');
        let fileNameElement = clone.querySelector('.ChatMessageFileContentName');
        if (fileDownloadElement && fileNameElement) {
            let fileUrl = fileDownloadElement.href;
            let fileName = fileNameElement.textContent;
        
            // 創建下載鏈接
            let fileLink = document.createElement('a');
            fileLink.href = fileUrl;
            fileLink.textContent = Text['file_CopyShowText'] + fileName;
            fileLink.target = "_blank"; // 在新窗口打開
            fileLink.style.display = 'block'; // 使其顯示為 block
            fileLink.style.textAlign = 'center'; // 置中
        
            // 用下載鏈接替換文件下載元素
            fileDownloadElement.parentNode.replaceChild(fileLink, fileDownloadElement); // 替換文件下載元素
            tempDiv.appendChild(fileLink); // 將鏈接附加到臨時 div
            tempDiv.appendChild(document.createElement("br")); // 添加換行
        }
        
        // 將所有 a 標籤轉換為 block 顯示，讓它們換行
        let anchorTags = clone.querySelectorAll('a');
        anchorTags.forEach(anchor => {
            anchor.style.display = 'block'; // 將 a 標籤設置為 block 顯示
            anchor.style.textAlign = 'center'; // 置中
        });
        
        // 最後將整個克隆元素加入到臨時 div 中
        tempDiv.appendChild(clone);  // 添加其他非視頻、音頻、文件的內容
        tempDiv.appendChild(document.createElement("br")); // 添加換行符
      });
      // 將臨時 div 加入到 DOM 中，並選擇其所有子元素
      document.body.appendChild(tempDiv);
      console.log(tempDiv)
      window.getSelection().selectAllChildren(tempDiv);
      document.execCommand("Copy");
      window.getSelection().empty();
      document.body.removeChild(tempDiv);
    setTimeout(function () {
      btn.className = "satisfactionBtnCopy";
    }, 800);
  },

  onRelatedClick(content) {
    Chat.PageApp.inputQuestion = content;
    Chat._sendMessage();
  },

  onSatisfactionClick(btn, messageId, result) {
    // if(result=="initTeamsIcon"){
    //     btn.parentElement.children[0].src = "../../image/dislike_custom.png";
    //     btn.parentElement.children[1].src = "../../image/ok_custom.png";
    //     btn.parentElement.children[2].src = "../../image/like_custom.png";
    //     btn.parentElement.children[3].src = "../../image/copy_custom.png";
    // }
    let args = {
      messageId: messageId,
      result: result,
    };
    if(document.getElementById(messageId+'0').parentElement.querySelectorAll('.grayScaleIcon').length>0){
      switch(result){
        case "1":
          if(!document.getElementById(messageId+"1").className.includes("grayScaleIcon"))args.result = "clear";
          break;
        case "0":
          if(!document.getElementById(messageId+"0").className.includes("grayScaleIcon"))args.result = "clear";
          break;
        case "-1":
          if(!document.getElementById(messageId+"-1").className.includes("grayScaleIcon"))args.result = "clear";
          break;
      }
    }
    HttpQuery.doRequest(
      "/openapi/copilot/satisfaction",
      args,
      function (result) {
        ChatEvent.onSatisfactionClickTeams(btn, args);
        if (result.status) {
          if (args.result == -1) {
            // 顯示不滿意回饋
            Swal.fire({
              title: Text["satisfactTitle"],
              input: "textarea",
              inputAttributes: {
                autocapitalize: "off",
                maxlength: 200,
              },
              showCancelButton: true,
              confirmButtonColor: "#3085d6",
              confirmButtonText: Text["satisfactSend"],
              cancelButtonText: Text["satisfactCancel"],
              showLoaderOnConfirm: true,
              inputValidator: (value) => {
                if (!value) {
                  return Text["inputCannotBeEmpty"];
                }
                if (value.length > 200) {
                  return Text["inputTooLong"];
                }
              },
            }).then((result) => {
              if (result.isConfirmed) {
                let feedback = {
                  messageId: messageId,
                  result: result.value,
                };
                HttpQuery.doRequest(
                  "/openapi/copilot/feedback",
                  feedback,
                  function (result) {
                    if (result.status) {
                      Swal.fire({
                        text: Text["satisfactResult"],
                        icon: "success",
                        confirmButtonColor: "#3085d6",
                        confirmButtonText: Text["OK"],
                      });
                    }
                  }
                );
              }
            });
          }
        }
        ChatEvent.updateChatHistory();
      }
    );
  },
  onSatisfactionClickTeams(btn,args) {
    if (args.result === "clear") {
      if (!Config.CUSTOM_CSS_ENABLE) {
        btn.parentElement.children[0].className =
          "satisfactionBtnDislike animate__animated animate__tada";
        btn.parentElement.children[1].className =
          "satisfactionBtnOk animate__animated animate__tada";
        btn.parentElement.children[2].className =
          "satisfactionBtnLike animate__animated animate__tada";
      } else {
        btn.parentElement.children[0].src =
          "../../image/custom-style-01/dislike_custom.png";
        btn.parentElement.children[1].src =
          "../../image/custom-style-01/ok_custom.png";
        btn.parentElement.children[2].src =
          "../../image/custom-style-01/like_custom.png";
      }
    } else {
      if (!Config.CUSTOM_CSS_ENABLE) {
        btn.parentElement.children[0].className =
          args.result == -1
            ? "satisfactionBtnDislike animate__animated animate__tada"
            : "grayScaleIcon satisfactionBtnDislike";
        btn.parentElement.children[1].className =
          args.result == 0
            ? "satisfactionBtnOk animate__animated animate__tada"
            : "grayScaleIcon satisfactionBtnOk";
        btn.parentElement.children[2].className =
          args.result == 1
            ? "satisfactionBtnLike animate__animated animate__tada"
            : "grayScaleIcon satisfactionBtnLike";
      } else {
        if (args.result == -1) {
          btn.parentElement.children[0].src =
            "../../image/custom-style-01/dislikeclick_custom.png";
          btn.parentElement.children[1].src =
            "../../image/custom-style-01/ok_custom.png";
          btn.parentElement.children[2].src =
            "../../image/custom-style-01/like_custom.png";
        } else if (args.result == 0) {
          btn.parentElement.children[0].src =
            "../../image/custom-style-01/dislike_custom.png";
          btn.parentElement.children[1].src =
            "../../image/custom-style-01/okclick_custom.png";
          btn.parentElement.children[2].src =
            "../../image/custom-style-01/like_custom.png";
        } else {
          btn.parentElement.children[0].src =
            "../../image/custom-style-01/dislike_custom.png";
          btn.parentElement.children[1].src =
            "../../image/custom-style-01/ok_custom.png";
          btn.parentElement.children[2].src =
            "../../image/custom-style-01/likeclick_custom.png";
        }
      }
    }
    setTimeout(function () {
      btn.classList.remove("animate__animated", "animate__tada");
    }, 800);

  },
  doStorageTOPN_KEY(args,messageId){
    if (!(args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠"))) {
      let saveData = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY);
      saveData[messageId] = args.topN;
      CommonUtil.setLocalStorage(MessageController.StorageTOPN_KEY, JSON.stringify(saveData));
    }
  },

  onViewMoreClick(btn, inputQuestion, messageId) {
    let data = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY)[
      messageId
    ];
    let title = Text["viewMore"];
    parent.parent.Window.QbiCopilotWebView.setListView(
      title,
      inputQuestion,
      data
    );
  },

  onAnswerButtonClick(option, code) {
    switch (option) {
      case Chat.DATA_KM:
        let data = CommonUtil.getData(Chat.DATA_KM, code);
        Chat.Chat_inputQuestion = data.sourceName;
        let content = MessageController.text(data.sourceName);
        MessageController.doAddMessage(content, MessageController.RIGHT);
        ActionCopilot.doDisaplyAnswer(
          data.messageId,
          code,
          data.chunk,
          function (ret) {
            console.debug("[ChatEvent-onAnswerButtonClick] success.");
          },
          true
        );
        break;
      case Chat.DATA_QA:
        ActionQbiBotPlus.doDisaplyAnswer(null, code, true);
        break;
      case "Option":
        ChatEvent.clickOptionButton(code);
        break;
      case "Url":
        ChatEvent.OpenP4Page(code);
        break;
    }
  },
  OpenP4Page(url) {
    let urldecode = decodeURIComponent(url);
    let urlTemp = new URL(urldecode);
    // 使用 URLSearchParams 解析查詢字串
    let urlParams = new URLSearchParams(urlTemp.search);
    // 讀取參數
    let isWebView = urldecode.includes("_webview"); // 讀取 _webview 參數
    let isAutoSendMessage =
      urlParams.has("question") && urlParams.has("isShowQuestion"); // 讀取 question 、isShowQuestion 參數

    if (isWebView || isAutoSendMessage) {
      if (urlParams.has("question")) {
        let question = urlParams.get("question").replace("_webview=1", "");
        if (urlParams.has("isShowQuestion")) {
          let isShowQuestion = urlParams.get("isShowQuestion") === "1";
          if (isShowQuestion) {
            Chat.PageApp.inputQuestion = question;
            Chat._sendMessage();
          } else {
            Chat.Chat_isShowQuestion = false;
            Chat.PageApp.inputQuestion = question;
            Chat._sendMessage();
            Chat.Chat_isShowQuestion = true;
          }
        }
      }
      if (isWebView) {
        // urlTemp.searchParams.delete("_webview");
        if(url?.includes('maps.google.com')){
          parent.parent.Window.QbiCopilotWebView.setWebView(
            parent.parent.window.document.title,
            urlTemp.href,
            "URL",
            true
          );
        }else{
          parent.parent.Window.QbiCopilotWebView.setWebView(
            parent.parent.window.document.title,
            urlTemp.origin,
            "URL",
            true
        );
    }
      } else {
        window.open(urlTemp.href);
      }
    } else {
      window.open(urlTemp.href);
    }
  },
  KeyWordsendMessage(inputQuestion) {
    Chat.PageApp.inputQuestion = inputQuestion;
    Chat._sendMessage();
  },
  clickOptionButton(link) {
    console.log(link)
    link = JSON.parse(decodeURIComponent(link))
    Chat.PageApp.inputQuestion = link.FCode;
    Chat.Chat_ChanegeShowQuestion = true;
    Chat.Chat_showQuestion = link.FDisplayText || link.FShowText;
    Chat._sendMessage();
    //通路顯示文字功能不受非同步影響
    Chat.Chat_ChanegeShowQuestion = false;
  },
  startChat() {
    return new Promise((resolve, reject) => {
      HttpQuery.doRequest(
        "/openapi/copilot/startAIRoom",
        { chatId: UserInfo.getData().userId },
        function (result) {
          if (result._header_.success) {
            ChatEvent.doSetNewChatInfo(result);
            resolve(result.roomId);
          }
        }
      );
    });
  },
  doSetNewChatInfo(result) {
    let data = {
      "FId": result.roomId,
      "roomId": result.roomId,
      "FCreateTime": new Date(),
      "FName": Text['newChat'],
      "FStatus": "convert",
      "isNewStartChat": true,
      "isCleanChat": true,
    }
    Chat.Chat_chatRoomId = result.roomId;
    Chat.cleanMessage();
    CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"AddNewChatToHistoryList" ,data);
  },
  stopChat() {
    return new Promise((resolve, reject) => {
      HttpQuery.doRequest(
        "/openapi/copilot/stopAIRoom",
        { 
          roomId: Chat.Chat_chatRoomId ,
          isCopilot: true, 
        },
        function (result) {
          if (result._header_.success) {
            Chat.Chat_chatRoomId = "";
            resolve(result);
          }
        }
      );
    });
  },
  saveChatMessage() {
    let answerMode = Chat.PageApp.ServerConfig.answerMode;
    switch (Chat.Chat_dataSource) {
      case "qa":
        break;
      case "km":
        if (ActionCopilot.ANSWER_GPT === answerMode)
          Chat.Chat_answerType = "gptText";
        break;
      case "function":
        Chat.Chat_kmIds = [];
        break;
      default:
        switch (Chat.Chat_answerType) {
          case "text":
            Chat.Chat_kmIds = [];
            break;
        }
        break;
    }

    let args = {
      inputQuestion: Chat.Chat_inputQuestion,
      answer: Chat.Chat_answer,
      dataSource: Chat.Chat_dataSource,
      answerType: Chat.Chat_answerType,
      channel: "copilot",
      chatId: UserInfo.getData().userId,
      messageId: Chat.Chat_messageId,
      userId: UserInfo.getData().userId,
      roomId: Chat.Chat_chatRoomId,
      kmIds: Chat.Chat_kmIds,
      isLogin: parent.Main.PageApp.isLogin,
      views: {
        "views": ChatEvent.removeHtmlTag('messageList',[".ViewMoreContainer"])
      },
    };
    return new Promise((resolve, reject) => {
      HttpQuery.doRequest(
        "/openapi/copilot/saveAIMessage",
        args,
        function (result) {
          if (result._header_.success) {
            ChatEvent.handleFirstMessageInRoom({roomId:Chat.Chat_chatRoomId, inputQuestion:Chat.Chat_inputQuestion});
            resolve();
          } else {
            console.log("request saveAIMessage error");
          }
        }
      );
    });
  },
  
  removeHtmlTag(containerId , classList){
    let html = document.getElementById(containerId).innerHTML;

    let parser = new DOMParser();
    let doc = parser.parseFromString('<div>' + html + '</div>', 'text/html'); // 用 div 包起來避免多根節點問題

    classList.forEach(classNameOrId => {
      doc.querySelectorAll(classNameOrId).forEach(el => el.remove());
    });
    return doc.body.firstChild.innerHTML;
  },

  handleFirstMessageInRoom(data){
    if(data)CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"handleFirstMessageInRoom" ,data);
  },

  cleanChatRecord() {
      //使用者新增問答時，對話紀錄調回預設
      Chat.Chat_dataSource = "error";
      Chat.Chat_answerType = "";
      Chat.Chat_answer = "";
      Chat.Chat_messageId = "";
      Chat.Chat_kmIds = [];
  },

  _showError(error) {
      try {
          let content;
          if (error !== undefined) {
              content = MessageController.text(error);
          } else {
              content = MessageController.text(Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['chatError']: Chat.PageApp.ServerConfig?.noAnswerReply);
          }
          Chat.Chat_answer = JSON.stringify({ text: content, type: "text" });
          Chat.Chat_dataSource = "noAnswer";
          Chat.Chat_answerType = "text";
          MessageController.doAddMessage(content, MessageController.LEFT);
      } catch (e) {
      } finally {
          Chat.PageApp.LOCK_SEND = false;
      }
  },
  getLatestMessageIndexFromHTML(htmlString) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");
  const messageElements = doc.querySelectorAll("[id^='Message_']");
  let maxIndex = 0;

  messageElements.forEach(el => {
    const match = el.id.match(/^Message_(\d+)$/);
    if (match) {
      const index = parseInt(match[1], 10);
      if (index > maxIndex) {
        maxIndex = index;
      }
    }
  });

  return maxIndex;
},

  updateChatHistory() {
    let args = {
      rid: Chat.Chat_chatRoomId,
      views: {
        "views": ChatEvent.removeHtmlTag('messageList',[".ViewMoreContainer"])
      },
    };
    return new Promise((resolve, reject) => {
      HttpQuery.doRequest(
        "/openapi/copilot/updateChatHistory",
        args,
        function (result) {
          if (result._header_.success) {
            resolve();
          } else {
            console.log("request updateChatHistory error");
          }
        }
      );
    });
  },
};
