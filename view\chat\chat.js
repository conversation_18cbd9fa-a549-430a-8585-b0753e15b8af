var Chat = {

    SEND_MESSAGE_NOW_HEIGHT: 60,
    SEND_MESSAGE_DEFAULT_HEIGHT: 60,
    PARAMETER: {},

    DATA_KM: 'DATA_KM',
    DATA_QA: 'DATA_QA',

    isAutoSpeechSynthesis: false,
    isLastHistory : false,
    Chat_dataSource: "",
    Chat_answerType: "",
    Chat_answer: "",
    Chat_messageId: "",
    Chat_kmIds: [],
    Chat_chatRoomId: "",
    Chat_inputQuestion: "",
    Chat_showQuestion: "",
    Chat_isShowQuestion: true,
    Chat_ChanegeShowQuestion: false,
    Chat_HistoryTimestamp: null,
    Chat_HistoryMessageArray  : [],
    doLoad() {
        if (Config.ENABLE_DEBUG) {
            var vConsole = new VConsole();
        }
        this.initApp();
        MessageController.doInit();
        CommonUtil.deleteLocalStorage(Chat.DATA_KM);
        CommonUtil.deleteLocalStorage(Chat.DATA_QA);
    },

    initApp() {
        Chat.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                inputQuestion: '',

                isMicroSendIcon: true,
                isMicroPhoneTurn: false,
                QbiCopilotInfo: { isTTSEnable: false, isSTTEnable: false, checkKeyWord: false, enableWebSearch: false },
                isWebSearchForceEnable: Config.FORCE_WEB_SEARCH_ENABLE,
                NoticeContent: { content: null, noticeId: null },
                roomId: "",
                LOCK_SEND: false,
                averageVolume: 0,
                siriWave: null,
                mediaStream: null,
                usermode: "",

                isKnowledgeFirst: true,

                NowCatalog: { FId: 'all', FName: Text['defaultCatalog'], hint: Text['defaultCatalog'] },
                CatalogItems: [],
                CatalogSelects: [],
                historyList:[],
                TOKEN_MAX: 1000,
                IS_CLEAR_TASK: true,
                ServerConfig: { functionEnable: false },
                FunctionItems: [],
                KM_COPILOT_HISTORY_ID:parent.parent.EcpController.KM_COPILOT_HISTORY_ID,
                NowSelectFunction: {
                    functionName: Text['functionLoading']
                }
            },
            filters: {
                truncate: function (value) {
                    if (!value) return '';
                    if (value.length <= 14) return value;
                    return value.substring(0, 14) + '...';
                }
            },
            methods: {
                getTokensCount(content) {
                    return gpt3encoder.countTokens(content);
                },
                sendMessage() {
                    Chat._sendMessage();
                },
                cleanMessageAndShowGreeting() {
                    Chat.cleanMessageAndShowGreeting();
                },
                WebSpeechRecognition() {
                    WebSpeechRecognition.doLoad(event);
                },
                openCatalogView() {
                    $('#catalogView').modal('toggle');
                },
                openKMSelectView() {
                    $('#KMSelectView').modal('toggle');
                },
                openFunctionView() {
                    $('#functionView').modal('toggle');
                },
                switchKnowledgeMode() {
                    Chat.switchKnowledgeMode();
                },
                choseCatalog(item) {
                    ChatCataLog.choseCatalog(item);
                },
                doCataLogSelect(item) {
                    ChatCataLog.doCataLogSelect(item);
                },
                _selectCataLog() {
                    ChatCataLog._selectCataLog();
                },
                choseFunction(item) {
                    Chat.PageApp.NowSelectFunction = item;
                    $('#functionView').modal('toggle');
                    if (Chat.PageApp.IS_CLEAR_TASK) {
                        MessageController.cleanChatMemory();
                    }
                }
            },
            watch: {
                inputQuestion: function (inputQuestion) {
                    Chat._doSetMessageBoxHeight(inputQuestion);
                },
                QbiCopilotInfo: function (QbiCopilotInfo) {
                    if (QbiCopilotInfo.functionEnable) {
                        document.getElementById('messageBox').style.height = "calc(100vh - 146.4px)"
                    } else {
                        document.getElementById('messageBox').style.height = "calc(100vh - 120px)"
                    }
                    parent.Main.PageApp.showAutoSpeech = QbiCopilotInfo.isTTSEnable;
                    Chat.PageApp.isMicroSendIcon = QbiCopilotInfo.isSTTEnable;
                    Chat.PageApp.usermode = CommonUtil.getLocalStorage('UserInfo').mode;
                    Chat.BeforeUnload();
                },
                LOCK_SEND(newVal, oldVal) {
                    CommonUtil.send(
                      parent.parent.EcpController.KM_COPILOT_HISTORY_ID,
                      "watchChatLock",
                      newVal
                    );
                }
            }
        });
        $('#App').show(100);
    },

    switchKnowledgeMode() {
        Chat.PageApp.isKnowledgeFirst = !Chat.PageApp.isKnowledgeFirst;

    },

    BeforeUnload() {
        if (Chat.PageApp.QbiCopilotInfo.isTTSEnable) {
            window.addEventListener('beforeunload', function (event) {
                Chat.speakingCancel();
            });
        }
        window.addEventListener('beforeunload', function (event) {
            const data = JSON.stringify({ roomId: Chat.Chat_chatRoomId });
            navigator.sendBeacon(Config.ECP_URL + '/openapi/copilot/stopAIRoom', data);
        });
    },

    changeFontSize(size) {
        document.getElementById('messageList').style.fontSize = (size === "l" ? "18px" : size === "m" ? "16px" : "14px");
    },

    disPlayMode(stringType) {
        if (Chat.PageApp.QbiCopilotInfo.isSTTEnable) {
            if (stringType == "siriWaveStart") {
                Chat.PageApp.siriWave = new SiriWave({
                    container: document.getElementById("siri-container"),
                    speed: 0.25,
                    height: 50,
                    amplitude: 0.08,
                });
                Chat.PageApp.siriWave.color = "48, 126, 218";
            } else if (stringType.startsWith("SiriWaveDuring")) {
                if (stringType == "SiriWaveDuring_Start") {
                    Chat.PageApp.siriWave.amplitude = 1.1;
                } else {
                    Chat.PageApp.siriWave.amplitude = 0.4;
                }

            } else if (stringType == "siriWaveEnd") {
                Chat.PageApp.siriWave.dispose();
            }
        }
    },

    onMessageBoxPress(event) {
        event = event || window.event;
        if (event.keyCode === 13 && !event.shiftKey) {
            event.returnValue = false;
            Chat._sendMessage();
            return false;
        }
    },

    onMessageBoxUp(event) {
        const sendMessageBox = document.getElementById('sendMessageBox');
        if (Chat.PageApp.QbiCopilotInfo.isSTTEnable) {
            if (sendMessageBox.value.trim() != '') {
                Chat.PageApp.isMicroSendIcon = false;
            } else {
                Chat.PageApp.isMicroSendIcon = true;
            }
        }
    },

    setSpeechDisPlay(mode) {
        const sendMessageBox = document.querySelector('#sendMessageBox');
        switch (mode) {
            case "init":
                Chat.PageApp.isMicroPhoneTurn = true;
                sendMessageBox.disabled = true;
                break;
            case "recognizing":
                if (Chat.PageApp.isMicroPhoneTurn) {
                    Chat.PageApp.siriWave.dispose();
                    Chat.PageApp.isMicroPhoneTurn = false;
                    sendMessageBox.disabled = false;
                } else {
                    Chat.PageApp.isMicroPhoneTurn = true;
                    sendMessageBox.disabled = true;
                    Chat.disPlayMode("siriWaveStart")
                }
                break;
            case "end":
                Chat.PageApp.isMicroPhoneTurn = false;
                sendMessageBox.disabled = false;
                Chat.disPlayMode("siriWaveEnd")
                break;
        }
    },
    doGetHistoryData(data) {
        if(Chat.PageApp.historyList.find(item => item.FId === Chat.Chat_chatRoomId)?.isNewStartChat){
            ChatEvent.stopChat().then((res) => {
                Chat.cleanMessage().then((result) =>{
                    if (Chat.Chat_chatRoomId == "" || Chat.Chat_chatRoomId == null) {
                        CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"doGetHistoryData" ,data);
                        return;
                    }else{
                        CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"doGetHistoryData" ,data);
                    }
                }) ;
            });
        }else{
            Chat.cleanMessage().then((result) =>{
                if (Chat.Chat_chatRoomId == "" || Chat.Chat_chatRoomId == null) {
                    CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"doGetHistoryData" ,data);
                    return;
                }else{
                    CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"doGetHistoryData" ,data);
                }
            }) ;
        }
    },
    doShowHistoryData(data) {
        Chat.Chat_chatRoomId = data.chatRoomId;
        if (data.historyData?.length > 0) {
            document.getElementById('messageList').innerHTML = data.historyData;
            MessageController.COUNT = ChatEvent.getLatestMessageIndexFromHTML(data.historyData)+1;
            MessageController._goChatBottom();
        }
        if (data.chat_history_list?.length > 0) {
            MessageController.doHandleHistoryToAddChatHistory(data.chat_history_list);
        }
    },

    cleanMessageAndShowGreeting() {
        Chat.cleanMessage().then((res) => {
            MessageController.doShowGreeting();
        });
    },
    cleanMessage() {
        return new Promise((resolve, reject) => {
        if (Chat.PageApp.LOCK_SEND) {
            return;
        }
        ChatEvent.SatisfactionRecord = [];
        Chat.PageApp.inputQuestion = '';
        Chat.Chat_chatRoomId = "";
        CommonUtil.send(Chat.PageApp.KM_COPILOT_HISTORY_ID,"cleanhistoryNowId" ,{});
        MessageController.cleanMessage();
        Chat.speakingCancel();
        ChatEvent.cleanChatRecord();
        QbiQuickReply.closeQuickReplyPool();
        parent.parent.EcpController.doWebViewToggle(true);
        Chat.PageApp.LOCK_SEND = false;
        resolve();
        });
    },

    sttSendMessage(sttWord) {
        const sendMessageBox = document.querySelector('#sendMessageBox');
        sendMessageBox.disabled = false;
        Chat.PageApp.isMicroPhoneTurn = false;
        Chat.PageApp.inputQuestion = sttWord;
        sendMessageBox.disabled = true;
        Chat.PageApp.isMicroPhoneTurn = true;
        Chat._sendMessage();
    },

    speakingCancel() {
        if (WebSpeechSynthesis.synth.speaking) { WebSpeechSynthesis.synth.cancel(); }
    },

    _sendMessage() {
        if (Chat.PageApp.LOCK_SEND) {
            return;
        }
        if (Chat.Chat_chatRoomId==="") {
            ChatEvent.startChat().then((data) => {
                Chat._sendMessage();
                return;
            });
        }
        Chat.speakingCancel();
        ChatEvent.cleanChatRecord();
        QbiQuickReply.closeQuickReplyPool();
        let questionLength = Chat.PageApp.inputQuestion.replace(/ |　/g, '').length;
        let token = gpt3encoder.countTokens(Chat.PageApp.inputQuestion);
        if (questionLength > 0 && token <= Chat.PageApp.TOKEN_MAX) {
            try {
                Chat.PageApp.LOCK_SEND = true;
                Chat.Chat_inputQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n|\/|\\|"/g, "");
                let originalQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n|\/|\\|"/g, "");
                let inputQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n/g, "<br>");
                let content = MessageController.text(inputQuestion);
                if(Chat.Chat_isShowQuestion){
                    MessageController.doAddMessage(content, MessageController.RIGHT);
                }
                Chat.PageApp.inputQuestion = "";

                // 一般Copilot問答流程
                let args = {
                    'login': UserInfo.getData().mode,
                    'chatId': UserInfo.getData().userId,
                    'inputQuestion': originalQuestion,
                }

                // @使用直通流程
                if (args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠")) {
                    ActionManager.get_km_answer(args);

                    // 是否有開啟Function call流程
                } else if (!Chat.PageApp.isKnowledgeFirst && ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE)) {
                    ActionManager.get_km_answer(args);
                } else if (Chat.PageApp.ServerConfig.functionEnable) {
                    ActionManager.do_function(args);
                    // 啟用混合式流程
                } else {
                    let answerMode = Chat.PageApp.ServerConfig.answerMode;
                    if (ActionCopilot.ANSWER_GPT_AND_KM == answerMode) {
                        ActionManager.do_QbiBotPlus(args);
                    } else if (ActionCopilot.ANSWER_GPT_PLUS_EASY == answerMode) {
                        ActionManager.do_QbiBotPlus(args);
                    }
                    else {
                        ActionManager.get_km_answer(args);
                    }
                }
            } catch (e) {
                console.error('[Chat _sendMessage ]' + e);
                ChatEvent._showError();
            }
        }
        setTimeout(() => {
            Chat.onMessageBoxUp();
        }, 100);
    },

    _doSetMessageBoxHeight(inputQuestion) {
        if (gpt3encoder.countTokens(inputQuestion) >= Chat.PageApp.TOKEN_MAX) {
            return;
        }
        let id = "sendMessageBox";
        let height = document.getElementById(id).scrollHeight;
        if (height != Chat.SEND_MESSAGE_NOW_HEIGHT) {
            let messageListHeight = document.getElementById('messageList').clientHeight;
            if (height < messageListHeight - 100) {
                $('#sendMessageBox').animate({ height: height }, 200);
                Chat.SEND_MESSAGE_NOW_HEIGHT = height;
            }
        }
        if (inputQuestion.length == 0) {
            $('#sendMessageBox').animate({ height: 60 }, 400);
            Chat.SEND_MESSAGE_NOW_HEIGHT = Chat.SEND_MESSAGE_DEFAULT_HEIGHT;
        }
    },
}
