.QbiAnswer_Container{
    display: table-cell;
    width: 95%;
}
.Multiple_Container{
    display: table-caption;
    width: 95%;
}
.MessageCard{
    width: 300px;
}
.QbiCardImage_square {
    width: 100%;
}

.QbiCardImage_rectangle {
    height: 200px;
    width: 100%;
}
.WordBreakAll{
    word-break: break-all;
}
.mySwiper {
    --swiper-navigation-color: #8ba8d9;
    --swiper-pagination-color: #8ba8d9;
}

.cardItem {
    padding: 0px;
}

.cardBtn {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/*-- 影片 --*/
.videoIframe {
    width: 100%;
    height: auto;
}


/*-- ChatMessageFileContent --*/

.ChatMessageFileContent {
    width: 260px;
    line-height: 20px;
}

.ChatMessageFileContentTop {
    border-bottom: 1px solid #d3d3d3;
    position: relative;
    padding: 6px 6px 4px 50px;
}

.ChatMessageFileContentBottom {
    padding: 3px 10px;
    text-align: right;
}

.ChatMessageFileContentIcon {
    position: absolute;
    width: 32px;
    height: 32px;
    left: 10px;
    top: 9px;
    background-image: url(../../../../../image/FileTypeUnknown.png);
}

.ChatMessageFileContentIconExcel {
    background-image: url(../../../../../image/FileTypeExcel.png);
}

.ChatMessageFileContentIconExe {
    background-image: url(../../../../../image/FileTypeExe.png);
}

.ChatMessageFileContentIconImage {
    background-image: url(../../../../../image/FileTypeImage.png);
}

.ChatMessageFileContentIconPpt {
    background-image: url(../../../../../image/FileTypePpt.png);
}

.ChatMessageFileContentIconText {
    background-image: url(../../../../../image/FileTypeText.png);
}

.ChatMessageFileContentIconWord {
    background-image: url(../../../../../image/FileTypeWord.png);
}

.ChatMessageFileContentIconZip {
    background-image: url(../../../../../image/FileTypeZip.png);
}

.ChatMessageFileContentName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ChatMessageFileContentInfo {
    color: #808080;
}

.ChatMessageFileContentStatus {
    padding-left: 16px;
}
.pointer{
    cursor:pointer
}

/*QuickReply*/

.QuickreplySlide {
    padding: 5px;
    margin: 3px 1px;
  }
  
  .QuickReply_div {
    padding: 5px;
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.1s;
  }
  
  .QuickReply_img {
    display: block;
    float: left;
    width: 30px;
    height: 30px;
    padding: 1px;
    border-radius: 60px;
    background-color: white;
  }
  
  #QuickReply_Container {
    margin-left: 5px !important;
  }
  
  .QuickReply_Vertical {
    display: flex;
    flex-wrap: wrap;
    height: 100%;
  }
  
/* QuickReply */
.QuickReply {
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    padding: 5px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    transition: background-color 0.1s;
}

.QuickReply_div:hover {
    background-color: rgb(255, 255, 255);
    -webkit-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}

/*midiaCard*/
  
.cardImage {
    border-radius: 10px 10px 0px 0px;
    height: 160px;
    width: 238px;
}
.divCenter {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.swiper_ul {
    list-style: none;
    text-align: center;
    display: block;
    padding: 0px;
    margin: 0;
    border-top: 1px solid #d1d1d1;
}
.swiper_ul>.swiper_li:last-child {
    padding-bottom: 8px;
}

.swiper_ul>.swiper_li {
    background-color: #fff;
    display: block;
    height: auto;
    overflow: visible;
    padding: 7px;
    margin: 0;
    color: #365899;
}

.swiper_ul>.swiper_li:last-child {
    border-radius: 0px 0px 10px 10px;
}

.swiper_ul>.swiper_li:hover {
    background-color: #d1d1d1;
    color: #365899;
    cursor: pointer;
}

.cardsSlide {
    border: 1px solid #d1d1d1 !important;
    border-radius: 10px;
}

