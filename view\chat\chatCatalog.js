var ChatCataLog = {

    cataLogMap: {},

    init() {
        try {
            HttpQuery.doRequest('/openapi/user/knowledge/topcatalog', {
                userId: UserInfo.getData().userId,
                loginType: UserInfo.getData().mode
            }, function (result) {
                let data = [];
                let defaultCataLog = { FId: 'all', FName: Text['defaultCatalog'], hint: Text['defaultCatalog'] };
                ChatCataLog.cataLogMap = {};
                data.push(defaultCataLog);
                if (result.status) {
                    for (let i = 0; i < result.items.length; i++) {
                        data.push(result.items[i]);
                        ChatCataLog.cataLogMap[result.items[i].FId] = result.items[i];
                        if(result.items[i].hasChild  && result.items[i].hasOwnProperty('items') ){
                            for (let j = 0; j < result.items[i].items.length; j++) {
                                ChatCataLog.cataLogMap[result.items[i].items[j].FId] = result.items[i].items[j];
                            }
                        }
                    }
                }
                defaultCataLog.hasChild = true;
                defaultCataLog.items = data;
                ChatCataLog.cataLogMap[defaultCataLog.FId] = defaultCataLog;
                Chat.PageApp.CatalogItems = data;
                Chat.PageApp.CatalogSelects.push(defaultCataLog);
            });
        } catch (e) {
            console.warn('[Chat initCatalog] not ready.');
            Swal.fire({
                icon: 'error',
                title: Text['loginFailTitle'],
                text: Text['loginFailDesc'],
                showCloseButton: false,
                showCancelButton: false,
                showConfirmButton: false,
            });
            setTimeout(function () {
                parent.Main.doLogout();
            }, 1000 * 3);
        }
    },

    choseCatalog(item) {

        Chat.PageApp.CatalogItems = item.items;
        if (item.FId !== 'all') {
            Chat.PageApp.CatalogSelects.push(item);
        }

    },

    doCataLogSelect(item) {
        if (item.hasChild && item.hasOwnProperty('items')) {
            let cataLogId = item.FId;
            let newArray = [];
            if (ChatCataLog.cataLogMap[cataLogId] == undefined) {
                return;
            }
            Chat.PageApp.CatalogItems = ChatCataLog.cataLogMap[cataLogId].items;
            for (let i = 0; i < Chat.PageApp.CatalogSelects.length; i++) {
                let id = Chat.PageApp.CatalogSelects[i].FId;
                newArray.push(Chat.PageApp.CatalogSelects[i]);
                if (id === item.FId) {
                    Chat.PageApp.CatalogSelects = newArray;
                    return;
                }
            }
        }
    },

    _selectCataLog() {
        item = {...Chat.PageApp.CatalogSelects[Chat.PageApp.CatalogSelects.length - 1]};
        item.hint = item.FName;
        Chat.PageApp.NowCatalog = item;
        $('#KMSelectView').modal('toggle');
    }
}