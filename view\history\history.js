// history.js
var HistoryPage = {
    doLoad() {
        this.initApp();
        this.initIframePost();
        if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
            console.log('[ History_initConfig] ecp mode.');

            // Copilot存在ECP底下
            Config.IS_ECP_MODE = true;
            console.log('[ History_initConfig] update IS_ECP_MODE = ' + Config.IS_ECP_MODE);

            Config.ECP_URL = parent.parent.Utility.getBaseUrl();
            console.log('[ History_initConfig] update ECP_URL = ' + Config.ECP_URL);
        }
    },

    initApp() {
        Vue.directive('focus', {
          inserted(el) {
            el.focus()
          }
        })
        HistoryPage.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                WebTitle: 'History',
                historyList: [],
                categorizedHistory: {
                  '今天': [],
                  '過去 7 天': [],
                  '過去 30 天': [],
                  '月份': {},
                  '年份': {}
                },
                historyNowId: null, // 當前歷史紀錄ID
                showMenuId: null,    // 哪個項目的三點選單顯示
                editingId: null,     // 哪個項目正在編輯
                tempName: "",            // 暫存輸入框內容
                fromIndex: 0,
                toIndex: 20,
                isHistoryDataLoading: false,
                isHistoryListLoading: false,
                noMoreData: false,
                isLock: false,
            },
            methods: {
              cleanMessageAndShowGreeting() {
                CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"cleanMessageAndShowGreeting" ,{});
              },
                // 取得歷史紀錄資訊
              doGetHistoryData(roomId) {
                if (HistoryPage.PageApp.isHistoryDataLoading || HistoryPage.PageApp.isLock || roomId === HistoryPage.PageApp.historyNowId) return;
                CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doGetHistoryData" ,{newRoomId: roomId});
              },
                // 切換三點選單顯示/隱藏
              toggleMenu(roomId) {
                  const isSameId = HistoryPage.PageApp.showMenuId === roomId;
                  HistoryPage.PageApp.showMenuId = null;
                  if (!isSameId) {
                    HistoryPage.PageApp.showMenuId = roomId;
                  }
              },
              
              // 開始重新命名
              startRename(roomId) {
                  HistoryPage.PageApp.editingId = roomId;
                  HistoryPage.PageApp.tempName = HistoryPage.PageApp.historyList.find(item => item.FId === roomId).FName;
                  HistoryPage.PageApp.showMenuId = null;
              },
              // 失焦時自動保存
              applyRename(roomId) {
                if (HistoryPage.PageApp.editingId === null) return;
              
                let item = HistoryPage.PageApp.historyList.find(i => i.FId === roomId);
                if (!item) return this._clearEditing();
              
                if (!HistoryPage.PageApp.tempName || item.FName === HistoryPage.PageApp.tempName) {
                  HistoryPage.PageApp.tempName = item.FName;
                  return this._clearEditing();
                }
                item.isCleanChat = false;
                item.FName = HistoryPage.PageApp.tempName;
                HistoryPage.doSetHistoryName(roomId, HistoryPage.PageApp.tempName,true);
                HistoryPage.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(HistoryPage.PageApp.historyList);
                CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
                this._clearEditing();
              },
              _clearEditing() {
                HistoryPage.PageApp.editingId = null;
                HistoryPage.PageApp.tempName = "";
              },
              // 刪除該筆紀錄
              deleteItem(roomId) {
                const index = HistoryPage.PageApp.historyList.findIndex(i => i.FId === roomId);
                if (index !== -1 && !HistoryPage.PageApp.isLock) {
                  HistoryPage.PageApp.historyList.splice(index, 1);
                  HistoryPage.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(HistoryPage.PageApp.historyList); // 重新分類
                  CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
                  // 刪除流程
                  if(HistoryPage.PageApp.historyNowId === roomId) {
                    HistoryPage.doDeleteHistoryData(roomId);
                    if(HistoryPage.PageApp.historyList.length === 0){
                      CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"cleanMessageAndShowGreeting" ,{});
                    }else{
                      HistoryPage.PageApp.historyNowId = HistoryPage.PageApp.historyList[0].FId;
                      HistoryPage.doGetHistoryData(HistoryPage.PageApp.historyList[0].FId);
                    }
                  }else{
                    HistoryPage.doDeleteHistoryData(roomId);
                  }
                }
                HistoryPage.PageApp.showMenuId = null;
              },
              // 滑鼠離開關閉menu
              handleMouseLeave(e) {
                  HistoryPage.PageApp.showMenuId = null; 
              },
              handleParentClick(e) {
                if (HistoryPage.PageApp.showMenuId !== null) {
                  HistoryPage.PageApp.showMenuId = null;
                }
              },
              handleScroll(e) {
                const el = e.target;
                const isBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10;
                if (isBottom && !HistoryPage.PageApp.isHistoryListLoading && !HistoryPage.PageApp.noMoreData) {
                  HistoryPage.loadHistoryList();
                }
              }
            },
            mounted() {
              const panel = this.$el.querySelector('.historyPanel');
              if (panel) {
                panel.addEventListener('scroll', this.handleScroll);
              }
            },
            beforeDestroy() {
              const panel = this.$el.querySelector('.historyPanel');
              if (panel) {
                panel.removeEventListener('scroll', this.handleScroll);
              }
            }
        });
    },
  // 載入歷史紀錄列表
  loadHistoryList() {
    if (CommonUtil.getLocalStorage('UserInfo').mode === 'anonymous' || HistoryPage.PageApp.isHistoryListLoading || HistoryPage.PageApp.noMoreData) return;
  
    HistoryPage.PageApp.isHistoryListLoading = true;
  
    HttpQuery.doRequest('/openapi/copilot/getAIRooms', {
      uid: CommonUtil.getLocalStorage('UserInfo').userId,
      fi: HistoryPage.PageApp.fromIndex,
      ti: HistoryPage.PageApp.toIndex,
    }, (result) => {
      const list = result.payload?.list || [];
  
      if (list.length === 0) {
        HistoryPage.PageApp.noMoreData = true;
      } else {
        HistoryPage.PageApp.historyList = HistoryPage.PageApp.historyList.concat(list)
        HistoryPage.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(HistoryPage.PageApp.historyList);
        CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
        HistoryPage.PageApp.fromIndex = HistoryPage.PageApp.toIndex + 1;
        HistoryPage.PageApp.toIndex = HistoryPage.PageApp.fromIndex + 9;
      }
      setTimeout(() => {
        HistoryPage.PageApp.isHistoryListLoading = false;
      }, 500); 
    });
  },
  cleanHistoryList() {
    HistoryPage.PageApp.historyList = [];
    HistoryPage.PageApp.categorizedHistory = {
      '今天': [],
      '過去 7 天': [],
      '過去 30 天': [],
      '月份': {},
      '年份': {}
    };
    HistoryPage.PageApp.fromIndex = 0;
    HistoryPage.PageApp.toIndex = 20;
    HistoryPage.PageApp.isHistoryDataLoading = false;
    HistoryPage.PageApp.isHistoryListLoading = false;
    HistoryPage.PageApp.noMoreData = false;
    HistoryPage.PageApp.historyNowId = null;
  },
  initIframePost() {
      CommonUtil.listen({
          AddNewChatToHistoryList: (data) => {
            HistoryPage.AddNewChatToHistoryList(data);
          },
          loadHistoryList: () => {
              HistoryPage.loadHistoryList();
          },
          handleFirstMessageInRoom: (data) => {
            HistoryPage.handleFirstMessageInRoom(data);
          },
          cleanHistoryList: () => {
            HistoryPage.cleanHistoryList();
          },
          watchChatLock: (isLock) => {
            HistoryPage.PageApp.isLock = isLock;
          },
          doGetHistoryData: (data) => {
            HistoryPage.doGetHistoryData(data.newRoomId);
          },
          cleanhistoryNowId: () => {
            HistoryPage.PageApp.historyNowId = null;
          },
      });
  },
  AddNewChatToHistoryList(data) {
    if(data != null){
      HistoryPage.PageApp.historyNowId = data.roomId;
      const list = [data];
      HistoryPage.PageApp.historyList = list.concat(HistoryPage.PageApp.historyList)
      HistoryPage.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(HistoryPage.PageApp.historyList);
      CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
    }
  },
  handleFirstMessageInRoom(data) {
    if(!!!data) return;
    let room = HistoryPage.PageApp.historyList.find(item => item.FId === data.roomId);
    if(room.FName === Text['newChat']){
      room.isCleanChat = false;
      room.FName = data.inputQuestion;
      HistoryPage.doSetHistoryName(data.roomId, data.inputQuestion,false);
      HistoryPage.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(HistoryPage.PageApp.historyList);
      CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
    }
  },
  categorizeHistoryList(historyList) {
    const now = new Date();
    const categories = {
      '今天': [],
      '過去 7 天': [],
      '過去 30 天': [],
      '月份': {},      
      '年份': {}     
    };
  
    historyList.forEach(item => {
      const nowDate = new Date();
      nowDate.setHours(0, 0, 0, 0); 
      const targetDate = new Date(item.FCreateTime);
      targetDate.setHours(0, 0, 0, 0);
      const diffDays = Math.floor((nowDate - targetDate) / (1000 * 60 * 60 * 24));
  
      if (diffDays === 0) {
        categories['今天'].push(item);
      } else if (diffDays <= 7) {
        categories['過去 7 天'].push(item);
      } else if (diffDays <= 30) {
        categories['過去 30 天'].push(item);
      } else if (targetDate.getFullYear() === now.getFullYear()) {
        const label = `${targetDate.getFullYear()} 年 ${targetDate.getMonth() + 1} 月`;
        if (!categories['月份'][label]) categories['月份'][label] = [];
        categories['月份'][label].push(item);
      } else {
        const label = `${targetDate.getFullYear()} 年`;
        if (!categories['年份'][label]) categories['年份'][label] = [];
        categories['年份'][label].push(item);
      }
    });
  
    return categories;
  },
  
  doGetHistoryData(roomId) {
    // 取得歷史紀錄資訊
    if (HistoryPage.PageApp.historyNowId != null) {
      const item = HistoryPage.PageApp.historyList.find(
          item => item.FId === HistoryPage.PageApp.historyNowId
      );
      if (item) {
          item.isNewStartChat = false;
      }
    }
    CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doUpdateHistoryList" ,HistoryPage.PageApp.historyList);
    HistoryPage.PageApp.isHistoryDataLoading = true;
    HistoryPage.PageApp.historyNowId = roomId;
    HttpQuery.doRequest('/openapi/copilot/getChatHistory', {
      uid: CommonUtil.getLocalStorage('UserInfo').userId,
      rid: roomId,
      fi: 0,
      ti: 1000,
    }, function (result) {
      if (result._header_.success) {
        CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID,"doShowHistoryData" 
          ,{
            historyData:result?.payload?.FHtmlView?.views,
            chat_history_list:result?.payload?.FLLMChatHistory?.chat_history_list,
            chatRoomId:roomId,
          });
      } else {
          console.log('更新失敗');
      }
      setTimeout(() => {
        HistoryPage.PageApp.isHistoryDataLoading = false;
      }, 1000); 
    });
  },
  doSetHistoryName(roomId, newName , isUserEdit) {
    HttpQuery.doRequest('/openapi/copilot/updateAIRoomName', {
        rid: roomId,
        rn: newName,
        isUserEdit: isUserEdit
    }, function (result) {
        if (result._header_.success) {
            // 更新成功
            console.log('更新成功');
        } else {
            // 更新失敗
            console.log('更新失敗');
        }
    });
  },
  doDeleteHistoryData(FId) {
    HttpQuery.doRequest('/openapi/copilot/deleteAIRoom', {
        rid: FId
    }, function (result) {
        if (result._header_.success) {
            // 刪除成功
            console.log('刪除成功');
        } else {
            // 刪除失敗
            console.log('刪除失敗');
        }
    });
  }
};