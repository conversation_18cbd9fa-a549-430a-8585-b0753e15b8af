var ActionQbiBotPlus = {

    Channel: 'web',

    execute(args) {
        // 先找QA範圍
        HttpQuery.doRequest('/openapi/copilot/getSummary', args, function (result) {
            try {
                // 錯誤回應
                if (result.hasOwnProperty('errorType') || result?.httpCode===429|| result?.httpCode===400) {
                    ActionManager.handleErrorState(result);
                    return;
                }

                let summary = result.answer;
                let content = Text['searching'] + summary;
                args.summary = summary;
                ActionManager.isDoAddHistory = false;
                if (summary.length > 0) {
                    MessageController.doAddMessage(MessageController.notice(content), MessageController.LEFT);
                    ActionFunction.HISTORY_RECORD_HUMAN.message = summary;
                    ActionFunction.HISTORY_RECORD.messages[0] = ActionFunction.HISTORY_RECORD_HUMAN
                }else{
                    ActionFunction.HISTORY_RECORD_HUMAN.message = Chat.Chat_inputQuestion;
                    ActionFunction.HISTORY_RECORD.messages[0] = ActionFunction.HISTORY_RECORD_HUMAN
                }

                if (Chat.PageApp.NowCatalog.FId !== 'all') {
                    args.knowledgeCatalogID = Chat.PageApp.NowCatalog.FId;
                }

                if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot && Chat.PageApp.QbiCopilotInfo.checkKeyWord) {
                    Chat.PageApp.roomId = parent.parent.window.ChatEcp.getCurrentRoomId();
                }
                args.should = ['qa'];
                args.responseType = "QA";
                Chat.Chat_dataSource = "qa";
            } catch (e) {
                ChatEvent._showError();
                return;
            }

            //QA不處理外部搜尋、直通
            ActionQbiBotPlus.doFlowControl(args)
        });
    },

    doFlowControl(args) {
        try {
            ActionManager.showWaitingMessage();
            //關鍵字判斷
            if (Chat.PageApp.QbiCopilotInfo.checkKeyWord) {
                ActionQbiBotPlus.checkKeyWord(args).then((isKeyWord) => {
                    ActionQbiBotPlus.doGetTopN(args).then((result) => {
                        if (isKeyWord) {
                            ActionQbiBotPlus.doKeyWordResponse(args, result);
                        } else {
                            ActionQbiBotPlus.handleTopNResult(args, result);
                        }
                    });
                });
            } else {
                ActionQbiBotPlus.doGetTopN(args).then((result) => {
                    ActionQbiBotPlus.handleTopNResult(args, result);
                });
            }
        } catch {
            console.warn('[ActionQbiBotPlus - doFlowControl] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    handleTopNResult(args, result) {
        if (result.topN.length == 0) {
            // QA模式：無法找到topN直接往下走KM
            args.should = ["km", "file", "document"];
            args.responseType = "KM";
            Chat.Chat_dataSource = "km";
            ActionCopilot.doFlowControl(args, false);
        } else {
            //多意圖判斷顯示
            ActionManager.doShowIntentsQuestion(result);
            args.topN = result.topN;
            CommonUtil.setLocalStorage(ActionCopilot.KEY_LAST_ARGS, JSON.stringify(args));
            ActionQbiBotPlus.doGetAnswerResponse(args, result);
        }
    },
    checkKeyWord(args) {
        try {
            return new Promise((resolve, reject) => {
                HttpQuery.doRequest('/openapi/copilot/checkKeyWord', args, function (result) {
                    resolve(result.checkKeyWord);
                });
            });
        } catch {
            console.warn('[ActionCopilot - checkKeyWord] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    doGetAnswerResponse(args) {
        try {
            HttpQuery.doRequest('/openapi/copilot/getAnswerResponse', args, function (result) {
                if(result?.httpCode===429||result?.httpCode===400){
                    ActionManager.handleErrorState(result);
                    return;
                }
                result.topN = args.topN;
                Chat.Chat_answer = result;
                ActionFunction.HISTORY_RECORD_AI.message = 
                result?.answerType === "text" 
                ? result?.answer 
                : result?.answer?.answers.map(item => item.answer).join('');
                let answerMode = Chat.PageApp.ServerConfig.answerMode;
                ChatEvent.doStorageTOPN_KEY(args,result.messageId);

                if (ActionCopilot.ANSWER_GPT_PLUS_EASY == answerMode) {
                    ActionQbiBotPlus.doParseEasyResponse(result, args.topN, args);
                } else {
                    ActionQbiBotPlus.doParseResponse(result, args.topN, args);
                }
                if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                    WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
                }
            });
        } catch {
            console.warn('[ActionQbiBotPlus - doGetAnswerResponse] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    doGetTopN(args) {
        try {
            return new Promise((resolve, reject) => {
                HttpQuery.doRequest('/openapi/copilot/getTopN', args, function (result) {
                    try {
                        resolve(result)
                    } catch (e) {
                        CommonUtil.deleteLocalStorage(ActionCopilot.KEY_LAST_ARGS);
                        ChatEvent._showError();
                        return;
                    }
                });
            });
        } catch {
            console.warn('[ActionQbiBotPlus - doGetTopN] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    doKeyWordResponse(args, result) {
        try {
            return new Promise((resolve, reject) => {
                try {
                    if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
                        if (Chat.PageApp.roomId) {
                            parent.parent.window.ChatEcp.keyWordToKnowlege({ roomId: Chat.PageApp.roomId, keyWord: args.inputQuestion });
                        }
                        Chat.PageApp.roomId = null;
                    }
                    HttpQuery.doRequest('/openapi/copilot/generateQuestion', {
                        inputQuestion: args.inputQuestion,
                        login: args.login,
                        chatId: args.chatId,
                        topN: result.topN
                    }, function (keyWordresult) {
                        if (keyWordresult.questions && keyWordresult.questions.length !== 0) {
                            //儲存對話紀錄        
                            Chat.Chat_answer = keyWordresult;
                            Chat.Chat_answerType = "Cards";
                            Chat.Chat_dataSource = "llm";
                            MessageController.doDisaplayKeyWordCard(keyWordresult.questions);
                        }
                        else {
                            // QA模式：無法找到topN直接往下走KM
                            args.should = ["km", "file", "document"];
                            args.responseType = "KM";
                            Chat.Chat_dataSource = "km";
                            ActionCopilot.doFlowControl(args, false);
                        }
                    });
                } catch (e) {
                    let content = MessageController.text(Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['referenceError']: Chat.PageApp.ServerConfig?.noAnswerReply);
                    Chat.Chat_answerType = "Text";
                    Chat.Chat_dataSource = "error";
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                    console.warn('[ActionCopilot - generateQuestion] format wrong..');
                }
            })
        } catch {
            console.warn('[ActionCopilot - doKeyWordResponse] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },

    doParseResponse(response, topN, args) {
        try {
            let answerMode = Chat.PageApp.ServerConfig.answerMode;
            let answerType = response.answerType;
            if ("gptText" == answerType) {
                let chunkMap = {};

                // topN 來源
                for (let i = 0; i < topN.length; i++) {
                    let topN_item = topN[i];
                    let chunkId = topN_item.metadata.chunkId;
                    if (!chunkMap.hasOwnProperty(chunkId)) {
                        chunkMap[chunkId] = topN_item;
                    }
                }
                args.chunkMap = chunkMap;
                // 顯示清單
                let answerSource = {};
                let answers = response.answer.answers;
                for (let i = 0; i < answers.length; i++) {
                    let sourceId = chunkMap[answers[i].chunkId].metadata.sourceId;
                    if (!answerSource.hasOwnProperty(sourceId)) {
                        answerSource[sourceId] = chunkMap[answers[i].chunkId];
                    }
                }

                args.answerSource = answerSource;

                // 呈現內容
                let showAnswer = Object.keys(answerSource);

                if (showAnswer.length == 1) {
                    // 如果只有一筆，顯示唯一的答案
                    let sourceId = showAnswer[0];
                    let messageId = response.messageId
                    //儲存對話紀錄        
                    Chat.Chat_messageId = messageId;
                    Chat.Chat_kmIds = [sourceId];
                    ActionQbiBotPlus.doDisaplyAnswer(messageId, sourceId, false);

                    // 防止Loading異常
                    if (showAnswer.length > 0) {
                        setTimeout(function () {
                            parent.Main.showLoading(false, 250);
                        }, 200 * showAnswer.length);
                    }
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                } else {
                    // 多筆知識，重新排序
                    ActionCopilot.doShowCards(args, response.messageId, true);
                }

            } else if ("text" == answerType) {
                args.should = ["km", "file", "document"];
                args.responseType = "KM";
                Chat.Chat_dataSource = "km";
                ActionCopilot.doFlowControl(args, false);
                return;
            }
        } catch {
            args.should = ["km", "file", "document"];
            args.responseType = "KM";
            Chat.Chat_dataSource = "km";
            ActionCopilot.doFlowControl(args, false);
            return;
        }
    },
    doParseEasyResponse(response, topN, args) {
        try {
            // let answerMode = Chat.PageApp.ServerConfig.answerMode;
            let answerType = response.answerType;
            if ("gptText" == answerType) {
                let chunkMap = {};
                // topN 來源
                for (let i = 0; i < topN.length; i++) {
                    let topN_item = topN[i];
                    let chunkId = topN_item.metadata.chunkId;
                    if (!chunkMap.hasOwnProperty(chunkId)) {
                        chunkMap[chunkId] = topN_item;
                    }
                }
                args.chunkMap = chunkMap;
                // 顯示清單
                let answerSource = {};
                let answer = [];
                response.answer["chunkId"] = response.answer["chunkId"].split(",")[0];
                answer.push(response.answer);
                try {
                    for (let i = 0; i < answer.length; i++) {
                        let sourceId = chunkMap[answer[i]["chunkId"]].metadata.sourceId;
                        if (!answerSource.hasOwnProperty(sourceId)) {
                            answerSource[sourceId] = chunkMap[answer[i]["chunkId"]];
                        }
                    }
                } catch {
                    answer = [];
                    for (let i = 0; i < topN.length; i++) {
                        let answerResult = Object.assign({}, topN[i]);
                        answerResult.count = Object.keys(sourcesMap).length + 1;
                        if (!sourcesMap.hasOwnProperty(answerResult.metadata.sourceId)) {
                            let answerItem = { chunkId: "", answer: "" }
                            answerItem["chunkId"] = answerResult.metadata.chunkId;
                            answerItem["answer"] = response.answer["answer"];
                            answer.push(answerItem);
                            sourcesMap[answerResult.metadata.sourceId] = answerResult;
                        }
                    }
                }

                args.answerSource = answerSource;

                // 呈現內容
                let showAnswer = Object.keys(answerSource);

                if (showAnswer.length == 1) {
                    // 如果只有一筆，顯示唯一的答案
                    let sourceId = showAnswer[0];
                    let messageId = response.messageId
                    ActionQbiBotPlus.doDisaplyAnswer(messageId, sourceId, false);

                    // 防止Loading異常
                    if (showAnswer.length > 0) {
                        setTimeout(function () {
                            parent.Main.showLoading(false, 250);
                        }, 200 * showAnswer.length);
                    }
                    Chat.PageApp.LOCK_SEND = false;
                    return;
                } else {
                    // 多筆知識，重新排序
                    ActionCopilot.doShowCards(args, response.messageId, true);
                }

            } else if ("text" == answerType) {
                args.should = ["km", "file", "document"];
                args.responseType = "KM";
                Chat.Chat_dataSource = "km";
                ActionCopilot.doFlowControl(args, false);
                return;
            }
        } catch {
            args.should = ["km", "file", "document"];
            args.responseType = "KM";
            Chat.Chat_dataSource = "km";
            ActionCopilot.doFlowControl(args, false);
            return;
            s
        }
    },

    doDisaplayAnswerCard(list) {
        let FQACardAnswer = [];
        for (let i = 0; i < list.length; i++) {
            let answer = list[i].metadata.sourceName;
            let answerId = list[i].metadata.sourceId;
            let item = {
                "FDisplayText": answer,
                "ValueContent": "",
                "FCode": answerId,
                "Option": Chat.DATA_QA,
                "FName": answer,
                "FShowText": answer
            };
            FQACardAnswer.push(item);
        }
        let cardAnswer = {
            "FQACardColumn": [
                {
                    "FQACardAnswer": FQACardAnswer,
                    "thumbnailImageUrl": "",
                    "FMsgAnswer": Chat.PageApp.ServerConfig.referDesc,
                    "type": "Cards",
                    "title": Chat.PageApp.ServerConfig.referTitle,
                }
            ],
            "type": "Cards",
        };
        QbiAnswer.doDisplayAnswer(null, JSON.stringify(cardAnswer));
    },

    doDisaplyAnswer(messageId, qId, showQuestion, callback) {
        MessageController.TotalMessage_Synthesis = "";
        let args = {
            qId: qId,
            channel: ActionQbiBotPlus.Channel
        }
        HttpQuery.doRequest('/openapi/copilot/getSmartQAItem', args, function (result) {
            try {
                if (result.success) {
                    if (showQuestion) {
                        let question = result.smartQA.FName;
                        Chat.Chat_inputQuestion = question;
                        let content = MessageController.text(question);
                        MessageController.doAddMessage(content, MessageController.RIGHT);
                    }
                    if (!(result.smartQA.FAnswerType_web == "QbiAnswer")) {
                        result.content = JSON.parse(result.content);
                        result.content.type = result.smartQA.FAnswerType_web
                        result.content = JSON.stringify(result.content);
                    }
                    if (result.smartQA.FContentType_web=="Json") {
                        result.content = JSON.parse(result.content);
                        result.content.type = result.smartQA.FContentType_web
                        result.content = JSON.stringify(result.content);
                    };
                    QbiAnswer.doDisplayAnswer(messageId, result.content);
                    if (callback != undefined) {
                        callback();
                    }
                } else {
                    Chat.Chat_answerType = "Text";
                    Chat.Chat_dataSource = "noAnswer";
                    MessageController.doAddMessage(MessageController.text(Text['answerEmpty']), MessageController.LEFT);
                }

                if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                    WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
                }
            } catch (e) {
                ChatEvent._showError();
                return;
            }
        });
    },

}